# 相机外参标定快速入门

## 5分钟快速上手

### 1. 准备工作

**所需材料：**
- 棋盘格标定板（推荐9×6格子）
- 相机（已完成内参标定）
- 5-10张不同角度的标定板图像

**环境要求：**
- 光照均匀，避免强烈阴影
- 标定板清晰可见，无运动模糊
- 标定板占据图像30-70%的区域

### 2. 图像采集指南

```
拍摄角度建议：
┌─────────────────┐
│  📷 正面视角     │  重投影误差通常最小
├─────────────────┤
│  📷 左倾15°     │  增加旋转约束
├─────────────────┤
│  📷 右倾15°     │  增加旋转约束  
├─────────────────┤
│  📷 上倾15°     │  增加平移约束
├─────────────────┤
│  📷 下倾15°     │  增加平移约束
├─────────────────┤
│  📷 近距离      │  提高角点精度
├─────────────────┤
│  📷 远距离      │  增加深度变化
└─────────────────┘
```

### 3. 命令行快速标定

```bash
# 基本用法
./camera_calibration --mode extrinsic --input ./images --output ./results

# 详细参数
./camera_calibration \
    --mode extrinsic \
    --input ./calibration_images \
    --output ./extrinsic_results \
    --config ./config/extrinsic_calibration.yaml
```

### 4. 结果解读

**标定成功的标志：**
- ✅ 平均重投影误差 < 1.0 像素
- ✅ 成功处理图像数 ≥ 5张
- ✅ 最大重投影误差 < 2.0 像素

**输出文件说明：**
```
results/
├── extrinsic_calibration_result.yaml  # 主要结果文件
├── calibration_report.html           # 可视化报告
├── reprojection_*.jpg                # 重投影可视化
└── camera_poses.txt                  # 简化格式的外参
```

**关键结果字段：**
```yaml
# 最终外参（推荐使用）
final_extrinsics:
  rotation_vector: [rx, ry, rz]      # 旋转向量（弧度）
  translation_vector: [tx, ty, tz]   # 平移向量（毫米）

# 质量指标
mean_reprojection_error: 0.234       # 平均重投影误差（像素）
num_successful_images: 8             # 成功标定的图像数
```

## 常见问题快速解决

### ❌ 问题1：角点检测失败
```
错误信息：No chessboard corners detected
解决方案：
1. 检查图像质量（清晰度、光照）
2. 确认标定板规格设置正确
3. 调整角点检测参数
```

### ❌ 问题2：重投影误差过大
```
错误信息：Mean reprojection error: 2.5 pixels
解决方案：
1. 检查相机内参是否准确
2. 确认标定板尺寸设置正确
3. 重新拍摄高质量图像
4. 启用Bundle Adjustment优化
```

### ❌ 问题3：标定结果不稳定
```
现象：多次标定结果差异较大
解决方案：
1. 增加标定图像数量
2. 确保图像角度多样性
3. 检查标定板是否平整
4. 使用三脚架固定相机
```

## 参数调优速查表

| 参数 | 默认值 | 调优建议 |
|------|--------|----------|
| 棋盘格尺寸 | 9×6 | 根据实际标定板调整 |
| 方格大小 | 25mm | 精确测量实际尺寸 |
| 重投影误差阈值 | 1.0 | 高精度应用可降至0.5 |
| Bundle Adjustment | 启用 | 建议始终启用 |
| 最大迭代次数 | 100 | 收敛慢时可增至200 |

## 质量检查清单

**拍摄前检查：**
- [ ] 相机内参已标定
- [ ] 标定板平整无损坏
- [ ] 光照条件良好
- [ ] 相机固定稳定

**拍摄时检查：**
- [ ] 标定板完整可见
- [ ] 图像清晰无模糊
- [ ] 角度变化充分
- [ ] 距离变化适当

**标定后检查：**
- [ ] 重投影误差合理
- [ ] 成功图像数充足
- [ ] 外参物理合理
- [ ] 可视化结果正常

## 进阶使用技巧

### 1. 批量处理多个数据集
```bash
# 使用脚本批量处理
for dir in dataset_*; do
    ./camera_calibration --mode extrinsic --input "$dir" --output "results_$dir"
done
```

### 2. 自定义配置文件
```yaml
# config/my_extrinsic_config.yaml
extrinsic_calibration:
  chessboard:
    board_size: [9, 6]
    square_size: 30.0  # 自定义方格大小
  
  optimization:
    enable_bundle_adjustment: true
    ba_max_iterations: 150
    ba_function_tolerance: 1e-7
  
  quality_control:
    max_reprojection_error: 0.8  # 更严格的误差要求
    min_required_images: 6
```

### 3. 编程接口使用
```cpp
#include "calibration/extrinsic_calibrator.h"

int main() {
    // 创建标定器
    ExtrinsicCalibrator calibrator;
    
    // 设置参数
    ExtrinsicCalibrationParams params;
    params.board_size = cv::Size(9, 6);
    params.square_size = 25.0;
    params.enable_bundle_adjustment = true;
    
    calibrator.updateParameters(params);
    
    // 执行标定
    std::vector<std::string> image_paths = {
        "img1.jpg", "img2.jpg", "img3.jpg"
    };
    
    ExtrinsicCalibrationResult result;
    auto error = calibrator.calibrateMultipleImages(image_paths, result);
    
    if (error == ErrorCode::SUCCESS) {
        std::cout << "标定成功！" << std::endl;
        std::cout << "平均重投影误差: " << result.mean_reprojection_error << std::endl;
        
        // 获取最终外参
        if (result.final_extrinsics.has_value()) {
            auto ext = result.final_extrinsics.value();
            std::cout << "旋转向量: " << ext.rotation_vector.t() << std::endl;
            std::cout << "平移向量: " << ext.translation_vector.t() << std::endl;
        }
    } else {
        std::cout << "标定失败: " << static_cast<int>(error) << std::endl;
    }
    
    return 0;
}
```

## 性能优化提示

### 1. 图像尺寸优化
- 大图像（>2000×2000）：考虑缩放至1000×1000
- 小图像（<500×500）：可能影响角点检测精度

### 2. 并行处理
```bash
# 使用多线程加速
export OMP_NUM_THREADS=4
./camera_calibration --mode extrinsic --input ./images --output ./results
```

### 3. 内存管理
- 大批量图像：分批处理避免内存溢出
- 高分辨率图像：使用图像金字塔降采样

## 故障排除

### 编译问题
```bash
# 检查依赖
pkg-config --modversion opencv4
pkg-config --modversion ceres

# 重新编译
cd build && make clean && cmake .. && make -j4
```

### 运行时问题
```bash
# 检查库路径
ldd ./camera_calibration

# 设置环境变量
export LD_LIBRARY_PATH=/usr/local/lib:$LD_LIBRARY_PATH
```

## 相关资源

- 📖 [完整技术文档](./camera_extrinsic_calibration_guide.md)
- 🔧 [配置文件说明](../config/extrinsic_calibration.yaml)
- 📊 [示例数据集](../data/sample_calibration_images/)
- 🐛 [问题反馈](https://github.com/your-repo/issues)

---

**💡 提示：** 首次使用建议先用示例数据集熟悉流程，然后再使用自己的数据进行标定。
