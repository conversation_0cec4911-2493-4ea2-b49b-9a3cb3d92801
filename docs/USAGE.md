# 相机标定系统使用指南

## 快速开始

### 1. 构建项目

```bash
# 克隆或复制项目到本地
cd camera_calibration_refactored

# 或者手动构建
mkdir build && cd build
cmake ..
make -j4
```

### 2. 准备配置文件

项目提供了两个主要配置文件：

- `config/config.yaml` - 主配置文件
- `config/camera_intrinsics.yaml` - 相机内参配置

根据您的实际情况修改这些配置文件中的参数。

### 3. 准备测试图像

将您的标定图像放在合适的位置，并在配置文件中指定路径：

```yaml
paths:
  input_image: "path/to/your/test_image.bmp"
  save_path: "./output/"
```

### 4. 运行标定

```bash
# 使用默认配置
./bin/camera_calibration

# 指定配置文件
./bin/camera_calibration --config config/config.yaml --intrinsics config/camera_intrinsics.yaml

# 覆盖输入输出路径
./bin/camera_calibration --input test_image.bmp --output ./results/

# 启用调试模式
./bin/camera_calibration --debug --verbose
```

## 详细使用说明

### 命令行参数

```
用法: camera_calibration [选项]

选项:
  -c, --config <path>      主配置文件路径 (默认: ../config/config.yaml)
  -i, --intrinsics <path>  相机内参文件路径 (默认: ../config/camera_intrinsics.yaml)
  -I, --input <path>       输入图像路径 (覆盖配置文件中的设置)
  -o, --output <path>      输出目录路径 (覆盖配置文件中的设置)
  -d, --debug              启用调试模式
  -v, --verbose            详细输出模式
  -h, --help               显示帮助信息
```

### 配置文件说明

#### 主配置文件 (config.yaml)

主要配置项包括：

1. **路径配置**
   ```yaml
   paths:
     input_image: "输入图像路径"
     save_path: "输出保存路径"
   ```

2. **图像尺寸配置**
   ```yaml
   image_dimensions:
     src_height: 720
     src_width: 1280
     output_height: 720
     output_width: 1280
   ```

3. **棋盘格参数**
   ```yaml
   chessboard:
     corners:
       rows: 11    # 内部角点行数
       cols: 8     # 内部角点列数
     bounds:       # 棋盘格边界区域
       x_min: 552
       y_min: 208
       x_max: 768
       y_max: 352
   ```

4. **Blob 检测参数**
   ```yaml
   blob_detector:
     color:
       filter_by_color: true
       blob_color: 0
     area:
       filter_by_area: true
       min_area: 200.0
       max_area: 100000.0
   ```

#### 相机内参配置 (camera_intrinsics.yaml)

```yaml
camera_parameters:
  intrinsics_and_distortion:
    - 686.54  # fx
    - 685.70  # fy
    - 625.40  # cx
    - 367.41  # cy
    - 0.176   # k1
    - -0.247  # k2
    - -0.001  # p1
    - -9.4e-06 # p2
    - 0.117   # k3
    # ... 更多畸变系数
```

### 标定流程

系统执行以下标定流程：

1. **配置加载和验证**
   - 加载主配置文件和相机内参
   - 验证配置参数的有效性
   - 检查输入文件和输出目录

2. **前向标定**
   - 读取输入图像
   - 检测棋盘格角点
   - 计算去畸变映射
   - 应用透视变换
   - 生成标定后的图像

3. **图像增强**
   - MSRCR 多尺度视网膜增强
   - 地面区域检测和增强
   - 黑色区域加深处理
   - 形态学操作

4. **特征检测**
   - SimpleBlobDetector 黑点检测
   - 特征点分类和标记
   - 验证检测结果

5. **坐标映射**
   - 像素坐标到世界坐标映射
   - 生成标定查找表
   - 保存标定数据

### 输出结果

标定完成后，系统会在输出目录生成以下文件：

1. **图像文件**
   - `calibrated_output.bmp` - 标定后的图像
   - `7out_enhance.bmp` - 增强后的图像
   - `8out_enhanceBolb.bmp` - 形态学处理后的图像
   - `9output_blob.bmp` - 特征点检测可视化
   - `9output_sorted.bmp` - 特征点标记图像

2. **数据文件**
   - `output.csv` - 特征点坐标数据
   - `distance_table` - 距离查找表
   - `mapx`, `mapy` - 去畸变映射表

3. **日志文件**
   - `Log.txt` - 处理日志

### 错误处理

常见错误及解决方法：

1. **配置文件加载失败**
   - 检查文件路径是否正确
   - 验证 YAML 语法是否正确

2. **图像加载失败**
   - 确认图像文件存在且格式支持
   - 检查文件权限

3. **棋盘格检测失败**
   - 调整棋盘格参数（行列数、边界区域）
   - 确保图像中棋盘格清晰可见

4. **特征点检测失败**
   - 调整 Blob 检测参数
   - 检查图像质量和光照条件

5. **点数验证失败**
   - 检查 `column_point_counts` 配置
   - 确保图像中的特征点分布符合预期

### 调试技巧

1. **启用调试模式**
   ```bash
   ./camera_calibration --debug --verbose
   ```

2. **检查中间结果**
   - 调试模式会保存所有中间处理结果
   - 查看各个处理步骤的输出图像

3. **调整参数**
   - 根据实际图像调整 Blob 检测参数
   - 修改图像增强参数以获得更好的效果

4. **查看日志**
   - 检查控制台输出和日志文件
   - 分析错误信息和警告

### 性能优化

1. **并行处理**
   ```yaml
   performance:
     parallel:
       enable: true
       num_threads: 0  # 0 = 自动检测
   ```

2. **内存优化**
   ```yaml
   performance:
     memory:
       max_image_cache_size: 100
       enable_memory_optimization: true
   ```

3. **图像尺寸**
   - 根据需要调整输出图像尺寸
   - 较小的图像可以提高处理速度

## 常见问题

### Q: 如何获得相机内参？
A: 可以使用 OpenCV 的相机标定功能，或者使用专业的标定工具。确保内参的准确性对标定结果很重要。

### Q: 棋盘格检测总是失败怎么办？
A: 
1. 确保棋盘格在图像中清晰可见
2. 调整 `chessboard.bounds` 参数
3. 检查 `chessboard.corners` 的行列数设置
4. 确保光照均匀，避免反光

### Q: 特征点检测结果不准确？
A: 
1. 调整 `blob_detector` 参数
2. 修改图像增强参数
3. 检查图像质量和对比度
4. 确保特征点（黑点）清晰可见

### Q: 如何验证标定结果的准确性？
A: 
1. 检查重投影误差
2. 使用已知距离的物体进行验证
3. 比较多次标定的结果一致性
4. 在不同距离和角度进行测试

### Q: 系统支持哪些图像格式？
A: 支持 OpenCV 支持的所有格式，包括 BMP、PNG、JPG、TIFF 等。

## 技术支持

如果遇到问题，请：

1. 查看详细的错误日志
2. 检查配置文件设置
3. 确认输入图像质量
4. 参考本文档的故障排除部分

更多技术细节请参考 API 文档和源代码注释。
