# 相机外参标定指南

## 概述

相机外参标定是确定相机在世界坐标系中位置和姿态的过程。本系统提供了基于棋盘格标定板的高精度外参标定功能，支持单张图像和多张图像标定模式。

## 理论基础

### 坐标系统

1. **世界坐标系 (World Coordinate System)**
   - 以标定板为基准的三维坐标系
   - 通常以标定板左上角为原点，X轴向右，Y轴向下，Z轴垂直向外

2. **相机坐标系 (Camera Coordinate System)**
   - 以相机光心为原点的三维坐标系
   - Z轴沿光轴方向，X轴和Y轴与图像坐标系对应

3. **图像坐标系 (Image Coordinate System)**
   - 二维像素坐标系
   - 原点通常在图像左上角

### 外参数学模型

外参由旋转和平移两部分组成：

```
P_camera = R * P_world + T
```

其中：
- `R`: 3×3旋转矩阵
- `T`: 3×1平移向量
- `P_world`: 世界坐标系中的点
- `P_camera`: 相机坐标系中的点

## 数据流程

### 1. 输入数据准备

```
输入图像 → 角点检测 → 坐标对应关系建立
    ↓
标定板参数 → 世界坐标生成
```

**关键数据结构：**
- `CalibrationImageData`: 存储单张图像的标定信息
- `CameraIntrinsics`: 相机内参（焦距、主点等）
- `DistortionCoefficients`: 畸变系数

### 2. 角点检测流程

```cpp
// 伪代码示例
bool detectChessboardCorners(const cv::Mat& image, 
                           std::vector<cv::Point2f>& corners) {
    // 1. 灰度化处理
    cv::Mat gray;
    cv::cvtColor(image, gray, cv::COLOR_BGR2GRAY);
    
    // 2. 棋盘格角点检测
    bool found = cv::findChessboardCorners(gray, board_size, corners);
    
    // 3. 亚像素精度优化
    if (found) {
        cv::cornerSubPix(gray, corners, cv::Size(11,11), cv::Size(-1,-1), criteria);
    }
    
    return found;
}
```

### 3. 外参求解流程

```
角点对应关系 → PnP求解 → 初始外参估计
    ↓
Bundle Adjustment → 外参优化 → 最终外参
    ↓
质量评估 → 重投影误差计算
```

## 标定流程详解

### 阶段1: 数据预处理

1. **图像加载与验证**
   ```cpp
   // 加载图像并检查有效性
   cv::Mat image = cv::imread(image_path);
   if (image.empty()) {
       return ErrorCode::IMAGE_LOAD_FAILED;
   }
   ```

2. **角点检测**
   ```cpp
   std::vector<cv::Point2f> image_points;
   bool corners_found = cv::findChessboardCorners(
       gray_image, board_size, image_points);
   ```

3. **世界坐标生成**
   ```cpp
   std::vector<cv::Point3f> object_points;
   for (int i = 0; i < board_size.height; ++i) {
       for (int j = 0; j < board_size.width; ++j) {
           object_points.push_back(cv::Point3f(
               j * square_size, i * square_size, 0.0f));
       }
   }
   ```

### 阶段2: 外参初始估计

使用PnP (Perspective-n-Point) 算法求解初始外参：

```cpp
cv::Mat rvec, tvec;
bool success = cv::solvePnP(
    object_points,    // 世界坐标点
    image_points,     // 图像坐标点
    camera_matrix,    // 相机内参矩阵
    dist_coeffs,      // 畸变系数
    rvec, tvec        // 输出：旋转向量和平移向量
);
```

### 阶段3: Bundle Adjustment优化

使用Ceres Solver进行非线性优化：

```cpp
// 重投影误差代价函数
class ReprojectionError {
    template <typename T>
    bool operator()(const T* const extrinsics, T* residuals) const {
        // 1. 世界坐标到相机坐标变换
        T camera_point[3];
        ceres::AngleAxisRotatePoint(extrinsics, object_point_, camera_point);
        camera_point[0] += extrinsics[3];  // tx
        camera_point[1] += extrinsics[4];  // ty
        camera_point[2] += extrinsics[5];  // tz
        
        // 2. 投影到图像平面
        T predicted_x = fx_ * camera_point[0] / camera_point[2] + cx_;
        T predicted_y = fy_ * camera_point[1] / camera_point[2] + cy_;
        
        // 3. 计算重投影误差
        residuals[0] = predicted_x - T(observed_x_);
        residuals[1] = predicted_y - T(observed_y_);
        
        return true;
    }
};
```

### 阶段4: 质量评估

1. **重投影误差计算**
   ```cpp
   double computeReprojectionError(
       const std::vector<cv::Point3f>& object_points,
       const std::vector<cv::Point2f>& image_points,
       const cv::Mat& rvec, const cv::Mat& tvec) {
       
       std::vector<cv::Point2f> projected_points;
       cv::projectPoints(object_points, rvec, tvec, 
                        camera_matrix, dist_coeffs, projected_points);
       
       double total_error = 0.0;
       for (size_t i = 0; i < image_points.size(); ++i) {
           cv::Point2f diff = image_points[i] - projected_points[i];
           total_error += sqrt(diff.x * diff.x + diff.y * diff.y);
       }
       
       return total_error / image_points.size();
   }
   ```

2. **统计信息计算**
   - 平均重投影误差
   - 最大重投影误差
   - 最小重投影误差
   - 标准差

## 多图像标定策略

### 全局外参计算

系统提供三种全局外参结果：

1. **最佳外参 (Best Extrinsics)**
   ```cpp
   // 选择重投影误差最小的图像外参
   core::CameraExtrinsics findBestExtrinsics(const std::vector<CalibrationImageData>& data) {
       double min_error = std::numeric_limits<double>::max();
       core::CameraExtrinsics best;
       
       for (const auto& img_data : data) {
           if (img_data.reprojection_error < min_error) {
               min_error = img_data.reprojection_error;
               best = img_data.extrinsics;
           }
       }
       return best;
   }
   ```

2. **平均外参 (Mean Extrinsics)**
   ```cpp
   // 对所有外参进行算术平均
   core::CameraExtrinsics computeMeanExtrinsics(const std::vector<CalibrationImageData>& data) {
       cv::Mat mean_rvec = cv::Mat::zeros(3, 1, CV_64F);
       cv::Mat mean_tvec = cv::Mat::zeros(3, 1, CV_64F);
       
       int count = 0;
       for (const auto& img_data : data) {
           if (img_data.corners_found) {
               mean_rvec += img_data.extrinsics.rotation_vector;
               mean_tvec += img_data.extrinsics.translation_vector;
               count++;
           }
       }
       
       mean_rvec /= count;
       mean_tvec /= count;
       
       core::CameraExtrinsics result;
       result.rotation_vector = mean_rvec;
       result.translation_vector = mean_tvec;
       return result;
   }
   ```

3. **最终外参 (Final Extrinsics)**
   - 当前实现中等于最佳外参
   - 可根据应用需求调整选择策略

## 使用方法

### 命令行接口

```bash
# 单张图像外参标定
./camera_calibration --mode extrinsic_single --input image.jpg --output ./results

# 多张图像外参标定
./camera_calibration --mode extrinsic --input ./images --output ./results
```

### 编程接口

```cpp
#include "calibration/extrinsic_calibrator.h"

// 创建标定器
ExtrinsicCalibrator calibrator;

// 设置参数
ExtrinsicCalibrationParams params;
params.board_size = cv::Size(9, 6);
params.square_size = 25.0;  // mm
params.enable_bundle_adjustment = true;

calibrator.updateParameters(params);

// 执行标定
ExtrinsicCalibrationResult result;
ErrorCode error = calibrator.calibrateMultipleImages(image_paths, result);

if (error == ErrorCode::SUCCESS) {
    // 获取最终外参
    if (result.final_extrinsics.has_value()) {
        auto final_ext = result.final_extrinsics.value();
        std::cout << "旋转向量: " << final_ext.rotation_vector << std::endl;
        std::cout << "平移向量: " << final_ext.translation_vector << std::endl;
    }
}
```

## 输出结果

### 标定结果文件结构

```yaml
# 标定统计信息
calibration_info:
  success: true
  num_successful_images: 10
  total_images: 12
  mean_reprojection_error: 0.234
  max_reprojection_error: 0.456
  min_reprojection_error: 0.123

# 最终全局外参
final_extrinsics:
  rotation_vector: [0.123, -0.456, 0.789]
  translation_vector: [12.34, -56.78, 123.45]
  rotation_matrix:
    - [0.987, -0.123, 0.098]
    - [0.134, 0.991, -0.012]
    - [-0.089, 0.034, 0.995]

# 平均外参
mean_extrinsics:
  rotation_vector: [0.120, -0.450, 0.785]
  translation_vector: [12.30, -56.70, 123.40]

# 每张图像的详细信息
camera_poses:
  - image_name: "image_001.jpg"
    corners_found: true
    reprojection_error: 0.234
    rotation_vector: [0.123, -0.456, 0.789]
    translation_vector: [12.34, -56.78, 123.45]
```

## 质量控制建议

### 1. 图像质量要求
- 图像清晰，无运动模糊
- 标定板完整可见
- 光照均匀，避免强烈阴影
- 标定板占据图像的30-70%区域

### 2. 拍摄策略
- 多角度拍摄（至少5-10张不同角度的图像）
- 包含不同距离的图像
- 覆盖图像的不同区域
- 避免标定板过于倾斜

### 3. 参数调优
- 重投影误差阈值：通常 < 1.0像素
- Bundle Adjustment迭代次数：50-200次
- 收敛阈值：根据精度要求调整

### 4. 结果验证
- 检查重投影误差分布
- 验证外参的物理合理性
- 使用独立测试图像验证精度

## 常见问题与解决方案

### 1. 角点检测失败
- **原因**: 图像质量差、标定板不完整、光照不均
- **解决**: 提高图像质量，调整检测参数

### 2. 重投影误差过大
- **原因**: 内参不准确、标定板尺寸错误、图像畸变严重
- **解决**: 重新标定内参，确认标定板参数

### 3. Bundle Adjustment不收敛
- **原因**: 初始估计过差、参数设置不当
- **解决**: 调整求解器参数，增加迭代次数

### 4. 多图像结果不一致
- **原因**: 图像质量参差不齐、拍摄条件变化
- **解决**: 筛选高质量图像，统一拍摄条件

## 实际应用示例

### 机器人视觉应用

```cpp
// 示例：机械臂抓取任务中的相机外参标定
class RobotVisionCalibration {
public:
    bool calibrateCameraToRobot() {
        // 1. 在机械臂工作空间内放置标定板
        // 2. 从多个角度拍摄标定板
        std::vector<std::string> calibration_images = {
            "robot_view_01.jpg", "robot_view_02.jpg",
            "robot_view_03.jpg", "robot_view_04.jpg"
        };

        // 3. 执行外参标定
        ExtrinsicCalibrationResult result;
        auto error = calibrator_.calibrateMultipleImages(calibration_images, result);

        if (error == ErrorCode::SUCCESS && result.final_extrinsics.has_value()) {
            // 4. 保存相机到机器人基座的变换矩阵
            camera_to_robot_transform_ = result.final_extrinsics.value();
            return true;
        }
        return false;
    }

    cv::Point3f transformToRobotCoordinates(const cv::Point2f& image_point, double depth) {
        // 将图像坐标转换为机器人坐标系
        // 这里需要结合深度信息和外参进行坐标变换
        // ...
    }
};
```

### 多相机系统标定

```cpp
// 示例：双目立体视觉系统的外参标定
class StereoCalibration {
public:
    struct StereoCameraExtrinsics {
        cv::Mat R;  // 右相机相对于左相机的旋转矩阵
        cv::Mat T;  // 右相机相对于左相机的平移向量
        cv::Mat E;  // 本质矩阵
        cv::Mat F;  // 基础矩阵
    };

    bool calibrateStereoSystem(
        const std::vector<std::string>& left_images,
        const std::vector<std::string>& right_images) {

        // 分别标定左右相机的外参
        ExtrinsicCalibrationResult left_result, right_result;

        auto left_error = left_calibrator_.calibrateMultipleImages(left_images, left_result);
        auto right_error = right_calibrator_.calibrateMultipleImages(right_images, right_result);

        if (left_error == ErrorCode::SUCCESS && right_error == ErrorCode::SUCCESS) {
            // 计算相机间的相对位姿
            computeRelativeExtrinsics(left_result, right_result);
            return true;
        }
        return false;
    }
};
```

## 高级优化技术

### 1. 鲁棒性优化

```cpp
// RANSAC算法提高外参估计的鲁棒性
class RobustExtrinsicEstimator {
public:
    bool estimateExtrinsicsRANSAC(
        const std::vector<cv::Point3f>& object_points,
        const std::vector<cv::Point2f>& image_points,
        cv::Mat& rvec, cv::Mat& tvec) {

        int max_iterations = 1000;
        double reprojection_threshold = 2.0;  // 像素
        int min_inliers = object_points.size() * 0.7;

        int best_inlier_count = 0;
        cv::Mat best_rvec, best_tvec;

        for (int iter = 0; iter < max_iterations; ++iter) {
            // 随机选择最小点集
            std::vector<int> sample_indices = randomSample(6, object_points.size());

            std::vector<cv::Point3f> sample_obj_pts;
            std::vector<cv::Point2f> sample_img_pts;
            for (int idx : sample_indices) {
                sample_obj_pts.push_back(object_points[idx]);
                sample_img_pts.push_back(image_points[idx]);
            }

            // 估计外参
            cv::Mat temp_rvec, temp_tvec;
            if (!cv::solvePnP(sample_obj_pts, sample_img_pts,
                             camera_matrix_, dist_coeffs_, temp_rvec, temp_tvec)) {
                continue;
            }

            // 计算内点数量
            int inlier_count = countInliers(object_points, image_points,
                                          temp_rvec, temp_tvec, reprojection_threshold);

            if (inlier_count > best_inlier_count) {
                best_inlier_count = inlier_count;
                best_rvec = temp_rvec.clone();
                best_tvec = temp_tvec.clone();
            }
        }

        if (best_inlier_count >= min_inliers) {
            rvec = best_rvec;
            tvec = best_tvec;
            return true;
        }
        return false;
    }
};
```

### 2. 在线标定与自适应更新

```cpp
// 在线外参标定系统
class OnlineExtrinsicCalibrator {
private:
    std::deque<CalibrationImageData> image_buffer_;
    const size_t max_buffer_size_ = 50;

public:
    bool updateExtrinsicsOnline(const cv::Mat& new_image) {
        CalibrationImageData new_data;

        // 检测新图像中的角点
        if (detectChessboardCorners(new_image, new_data)) {
            // 添加到缓冲区
            image_buffer_.push_back(new_data);

            // 保持缓冲区大小
            if (image_buffer_.size() > max_buffer_size_) {
                image_buffer_.pop_front();
            }

            // 如果有足够的图像，重新标定
            if (image_buffer_.size() >= 10) {
                std::vector<CalibrationImageData> current_data(
                    image_buffer_.begin(), image_buffer_.end());

                return optimizeExtrinsics(current_data);
            }
        }
        return false;
    }

    bool isCalibrationStable() const {
        // 检查最近几次标定结果的稳定性
        if (recent_results_.size() < 5) return false;

        double variance = computeExtrinsicsVariance(recent_results_);
        return variance < stability_threshold_;
    }
};
```

## 性能优化建议

### 1. 计算优化

```cpp
// 使用OpenMP并行化角点检测
void detectCornersParallel(const std::vector<cv::Mat>& images,
                          std::vector<CalibrationImageData>& results) {
    results.resize(images.size());

    #pragma omp parallel for
    for (size_t i = 0; i < images.size(); ++i) {
        detectChessboardCorners(images[i], results[i]);
    }
}

// GPU加速的角点检测（如果可用）
#ifdef USE_CUDA
void detectCornersGPU(const cv::cuda::GpuMat& gpu_image,
                     std::vector<cv::Point2f>& corners) {
    // 使用CUDA加速的角点检测算法
    // ...
}
#endif
```

### 2. 内存优化

```cpp
// 大图像的分块处理
class TiledCalibration {
public:
    bool calibrateLargeImage(const cv::Mat& large_image) {
        if (large_image.rows > 4000 || large_image.cols > 4000) {
            // 分块处理大图像
            std::vector<cv::Rect> tiles = generateTiles(large_image.size());

            for (const auto& tile : tiles) {
                cv::Mat tile_image = large_image(tile);
                if (processTile(tile_image, tile.tl())) {
                    return true;  // 找到有效的标定板
                }
            }
        } else {
            return processFullImage(large_image);
        }
        return false;
    }
};
```

## 错误诊断与调试

### 1. 标定质量诊断

```cpp
class CalibrationDiagnostics {
public:
    struct DiagnosticReport {
        bool overall_quality_good;
        std::vector<std::string> warnings;
        std::vector<std::string> errors;

        struct ImageQuality {
            std::string image_name;
            double reprojection_error;
            bool corners_detected;
            double corner_detection_confidence;
            bool recommended_for_use;
        };
        std::vector<ImageQuality> image_qualities;
    };

    DiagnosticReport diagnoseCalibration(const ExtrinsicCalibrationResult& result) {
        DiagnosticReport report;

        // 检查整体质量
        if (result.mean_reprojection_error > 1.0) {
            report.warnings.push_back("平均重投影误差较大: " +
                std::to_string(result.mean_reprojection_error));
        }

        if (result.num_successful_images < 5) {
            report.errors.push_back("有效图像数量不足: " +
                std::to_string(result.num_successful_images));
        }

        // 分析每张图像
        for (const auto& img_data : result.image_data) {
            DiagnosticReport::ImageQuality quality;
            quality.image_name = img_data.image_path;
            quality.reprojection_error = img_data.reprojection_error;
            quality.corners_detected = img_data.corners_found;

            // 评估图像质量
            quality.recommended_for_use =
                img_data.corners_found &&
                img_data.reprojection_error < 0.8;

            report.image_qualities.push_back(quality);
        }

        return report;
    }
};
```

### 2. 可视化调试工具

```cpp
class CalibrationVisualizer {
public:
    cv::Mat visualizeReprojection(const CalibrationImageData& data) {
        cv::Mat vis_image = data.image.clone();

        // 投影3D点到图像
        std::vector<cv::Point2f> projected_points;
        cv::projectPoints(data.object_points,
                         data.extrinsics.rotation_vector,
                         data.extrinsics.translation_vector,
                         camera_matrix_, dist_coeffs_, projected_points);

        // 绘制检测到的角点（绿色）
        for (const auto& pt : data.image_points) {
            cv::circle(vis_image, pt, 3, cv::Scalar(0, 255, 0), -1);
        }

        // 绘制重投影点（红色）
        for (const auto& pt : projected_points) {
            cv::circle(vis_image, pt, 3, cv::Scalar(0, 0, 255), 1);
        }

        // 绘制误差向量
        for (size_t i = 0; i < data.image_points.size(); ++i) {
            cv::line(vis_image, data.image_points[i], projected_points[i],
                    cv::Scalar(255, 0, 0), 1);
        }

        return vis_image;
    }

    void generateCalibrationReport(const ExtrinsicCalibrationResult& result,
                                  const std::string& output_dir) {
        // 生成HTML格式的标定报告
        std::ofstream html_file(output_dir + "/calibration_report.html");

        html_file << "<html><head><title>外参标定报告</title></head><body>";
        html_file << "<h1>相机外参标定结果</h1>";

        // 添加统计信息
        html_file << "<h2>标定统计</h2>";
        html_file << "<p>成功图像数: " << result.num_successful_images << "</p>";
        html_file << "<p>平均重投影误差: " << result.mean_reprojection_error << " 像素</p>";

        // 添加每张图像的可视化结果
        html_file << "<h2>图像详情</h2>";
        for (size_t i = 0; i < result.image_data.size(); ++i) {
            if (result.image_data[i].corners_found) {
                cv::Mat vis = visualizeReprojection(result.image_data[i]);
                std::string img_name = "reprojection_" + std::to_string(i) + ".jpg";
                cv::imwrite(output_dir + "/" + img_name, vis);

                html_file << "<div>";
                html_file << "<h3>" << result.image_data[i].image_path << "</h3>";
                html_file << "<p>重投影误差: " << result.image_data[i].reprojection_error << "</p>";
                html_file << "<img src='" << img_name << "' width='400'>";
                html_file << "</div>";
            }
        }

        html_file << "</body></html>";
        html_file.close();
    }
};
```

## 技术参考

- Zhang, Z. "A flexible new technique for camera calibration." IEEE TPAMI, 2000.
- Hartley, R. & Zisserman, A. "Multiple View Geometry in Computer Vision." 2003.
- Ceres Solver Documentation: http://ceres-solver.org/
- OpenCV Camera Calibration Tutorial: https://docs.opencv.org/
- Bouguet, J.Y. "Camera Calibration Toolbox for Matlab." Caltech, 2004.
