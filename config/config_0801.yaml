# 相机标定系统配置文件
# 版本: 1.0.0

# 文件路径配置
paths:
  input_image: "../data/input/0801/20250801-132442.bmp"  # 输入测试图像路径
  save_path: "output/"                       # 标定结果保存路径
  # debug_path: "debug/"

# 图像尺寸配置
image_dimensions:
  src_height: 1200      # 原始图像高度
  src_width: 1600      # 原始图像宽度
  output_height: 1200   # 输出图像高度
  output_width: 1600   # 输出图像宽度

# 图像处理范围配置
image_processing:
  range:
    x_range: [0, 1600]    # 图像中 X（列）方向的处理范围
    y_range: [680, 1200]    # 图像中 Y（行）方向的处理范围
    # y_range: [0, 720]    # 图像中 Y（行）方向的处理范围
  
  
  enhancement:
    dark_threshold: 150    # 黑色区域阈值，小于此值认为是黑色区域
    darken_amount: 60      # 黑色区域加深程度
    # MSRCR 参数
    msrcr:
      weights: [0.1, 0.1, 0.1]        # 多尺度权重
      sigmas: [30.0, 150.0, 300.0]    # 尺度参数
      gain: 128.0                     # 增益参数
      offset: 128.0                   # 偏移参数
    
    # 形态学操作参数
    morphology:
      kernel_size: [2, 2]    # 形态学核大小
      dilate_iterations: 1   # 膨胀迭代次数
      erode_iterations: 3    # 腐蚀迭代次数
    
    # 边缘处理
    border:
      width: 50              # 边缘漂白宽度
      bottom_height: 20      # 底部漂白高度

# 棋盘格参数配置
chessboard:
  corners:
    rows: 11          # 内部角点行数（注意不是格子数）
    cols: 8            # 内部角点列数
    square_size: 30.0
  
  # bounds:              # 原图标定板的边界区域
  #   x_min: 735         # 左上角 X 坐标
  #   y_min: 480         # 左上角 Y 坐标  
  #   x_max: 982         # 右下角 X 坐标
  #   y_max: 640         # 右下角 Y 坐标

  bounds:              # 原图标定板的边界区域
    x_min: 690         # 左上角 X 坐标
    y_min: 435         # 左上角 Y 坐标  
    x_max: 900         # 右下角 X 坐标
    y_max: 585         # 右下角 Y 坐标
  
  
  processing:
    bottom_threshold: 0    # 从图像底部往上裁切的像素值
    y_offset: 45           # 棋盘格最后一个角点向下扩展的像素值

# Blob 检测器参数配置
blob_detector:
  color:
    filter_by_color: true    # 是否按颜色过滤
    blob_color: 0           # 检测的颜色（0=黑色，255=白色）
  
  area:
    filter_by_area: true    # 是否按面积过滤
    min_area: 500.0         # 最小面积（像素）
    max_area: 100000.0      # 最大面积（像素）
  
  circularity:
    filter_by_circularity: true    # 是否按圆度过滤
    min_circularity: 0.05          # 最小圆度
    max_circularity: 0.99          # 最大圆度
  
  convexity:
    filter_by_convexity: false     # 是否按凸性过滤
    min_convexity: 0.87           # 最小凸性
    max_convexity: 1.0            # 最大凸性
  
  inertia:
    filter_by_inertia: false      # 是否按惯性比过滤
    min_inertia_ratio: 0.05       # 最小惯性比
    max_inertia_ratio: 0.99       # 最大惯性比


# 世界坐标系配置
world_coordinates:
  limits:
    x_min: 0           # 物理距离最小值（Z方向，单位：cm）
    x_max: 70          # 物理距离最大值（Z方向，单位：cm）
    y_min: -125        # 横向距离最小值（Y方向，单位：cm）
    y_max: 125         # 横向距离最大值（Y方向，单位：cm）
  
  grid_info:
    cols: 14           # 世界坐标网格列数
    rows: 7            # 世界坐标网格行数
    distance_to_camera: 1.0        # 相机到光心的距离（Z方向，单位：cm）
    distance_to_camera_center: 0.0 # 相机距离中心线的偏移（横向，单位：cm）
    
    # Z方向距相机的距离，0为相机位置（单位：cm）
    h_axis: [70, 52, 40, 30, 20, 10, 0]
    
    # 横向离相机距离，相机位于中间，左负右正（单位：cm）
    w_axis: [-32.5, -27.5, -22.5, -17.5, -12.5, -7.5, -2.5, 
             2.5, 7.5, 12.5, 17.5, 22.5, 27.5, 32.5]

# 特征点验证配置
feature_validation:
  # 左右列黑点个数要求
  column_point_counts:
    left:  [6, 6, 5, 4, 4, 3, 2]     # 左侧各列的点数要求
    right: [6, 6, 5, 4, 4, 3, 2]     # 右侧各列的点数要求
    ##左右列个数必须一致


## 外参标定配置
# PnP求解器配置
pnp_solver:
  method: "iterative"       # PnP方法: iterative, epnp, p3p, dls, upnp
  use_extrinsic_guess: false # 是否使用外参初值
  iterations_count: 100     # 迭代次数
  reprojection_error: 8.0   # 重投影误差阈值 (像素)
  confidence: 0.99          # 置信度

quality_control:
  max_reprojection_error: 2.0     # 最大允许重投影误差 (像素)
  min_required_images: 3          # 最少需要的图像数量
  min_baseline_ratio: 0.1         # 最小基线比例
