# 外参标定配置文件
# 版本: 1.0.0

# 标定模式配置
calibration:
  mode: "extrinsic"  # 可选: intrinsic, extrinsic, forward_mapping

# 外参标定参数
extrinsic_calibration:
  # 标定板配置
  calibration_board:
    type: "chessboard"        # 标定板类型: chessboard, circles_grid, asymmetric_circles_grid
    board_size: [11, 8]       # 标定板尺寸 [宽度, 高度] (内部角点数)
    square_size: 30.0         # 方格大小 (mm)

  # PnP求解器配置
  pnp_solver:
    method: "iterative"       # PnP方法: iterative, epnp, p3p, dls, upnp
    use_extrinsic_guess: false # 是否使用外参初值
    # 外参初值配置 (仅在use_extrinsic_guess为true时使用)
    initial_extrinsics:
      rotation_vector: [0.0, 0.0, 0.0]        # 初始旋转向量 [rx, ry, rz] (弧度)
      translation_vector: [0.0, 0.0, 500.0]   # 初始平移向量 [tx, ty, tz] (mm)
    iterations_count: 100     # 迭代次数
    reprojection_error: 8.0   # 重投影误差阈值 (像素)
    confidence: 0.99          # 置信度

  # Bundle Adjustment配置
  bundle_adjustment:
    enable: false              # 是否启用BA优化
    max_iterations: 100       # 最大迭代次数
    function_tolerance: 1e-6  # 函数收敛阈值
    parameter_tolerance: 1e-8 # 参数收敛阈值
    gradient_tolerance: 1e-10 # 梯度收敛阈值

  # # 优化选项
  # optimization:
  #   optimize_intrinsics: false      # 是否同时优化内参
  #   optimize_distortion: false      # 是否同时优化畸变
  #   fix_principal_point: false      # 是否固定主点
  #   fix_aspect_ratio: false         # 是否固定纵横比
  #   zero_tangent_distortion: false  # 是否设置切向畸变为0

  # 质量控制
  quality_control:
    max_reprojection_error: 2.0     # 最大允许重投影误差 (像素)
    min_required_images: 3          # 最少需要的图像数量
    min_baseline_ratio: 0.1         # 最小基线比例

  # 角点检测配置
  corner_detection:
    enable_subpixel_refinement: true  # 是否启用亚像素精度优化
    save_corner_images: false        # 是否保存带角点标记的图片

# 图像输入配置
image_input:
  input_directory: "/home/<USER>/panpan/code/Calib/CALIB_t11_intrix/0801/calib_imgs/"
  image_pattern: "*.bmp"    # 图像文件模式

# 结果输出配置
output:
  save_path: "./output/extrinsic_calibration/"
  save_extrinsics: true           # 保存外参结果
  save_reprojection_images: true # 保存重投影可视化图像
  save_statistics: true          # 保存统计信息
  extrinsics_format: "yaml"      # 外参保存格式: yaml, xml, json
