#ifndef CAMERA_CALIBRATION_CERES_BUNDLE_ADJUSTMENT_H
#define CAMERA_CALIBRATION_CERES_BUNDLE_ADJUSTMENT_H

#ifdef USE_CERES_SOLVER
#include <ceres/ceres.h>
#include <ceres/rotation.h>
#endif

#include <opencv2/opencv.hpp>
#include "core/types.h"

namespace camera_calibration {
namespace calibration {

#ifdef USE_CERES_SOLVER

/**
 * @brief Ceres Solver重投影误差代价函数 - 仅优化外参
 */
class ReprojectionErrorExtrinsicOnly {
public:
    ReprojectionErrorExtrinsicOnly(double observed_x, double observed_y,
                                  double object_point_x, double object_point_y, double object_point_z,
                                  const double* intrinsics, const double* distortion)
        : observed_x_(observed_x), observed_y_(observed_y)
        , object_point_x_(object_point_x), object_point_y_(object_point_y), object_point_z_(object_point_z) {
        // 复制固定的内参和畸变参数
        for (int i = 0; i < 4; ++i) intrinsics_[i] = intrinsics[i];
        for (int i = 0; i < 5; ++i) distortion_[i] = distortion[i];
    }

    template <typename T>
    bool operator()(const T* const extrinsics, T* residuals) const {
        // 将固定参数转换为模板类型
        T intrinsics_t[4];
        T distortion_t[5];
        for (int i = 0; i < 4; ++i) intrinsics_t[i] = T(intrinsics_[i]);
        for (int i = 0; i < 5; ++i) distortion_t[i] = T(distortion_[i]);

        return ComputeReprojectionError(extrinsics, intrinsics_t, distortion_t, residuals);
    }

private:
    double observed_x_, observed_y_;
    double object_point_x_, object_point_y_, object_point_z_;
    double intrinsics_[4];
    double distortion_[5];

    template <typename T>
    bool ComputeReprojectionError(const T* const extrinsics,
                                 const T* const intrinsics,
                                 const T* const distortion,
                                 T* residuals) const {
        // 外参：[rx, ry, rz, tx, ty, tz]
        const T* rotation = extrinsics;
        const T* translation = extrinsics + 3;

        // 3D点坐标
        T object_point[3];
        object_point[0] = T(object_point_x_);
        object_point[1] = T(object_point_y_);
        object_point[2] = T(object_point_z_);

        // 应用旋转和平移变换
        T camera_point[3];
        ceres::AngleAxisRotatePoint(rotation, object_point, camera_point);
        camera_point[0] += translation[0];
        camera_point[1] += translation[1];
        camera_point[2] += translation[2];

        // 投影到归一化图像平面
        T xp = camera_point[0] / camera_point[2];
        T yp = camera_point[1] / camera_point[2];

        // 应用畸变
        T r2 = xp * xp + yp * yp;
        T r4 = r2 * r2;
        T r6 = r4 * r2;

        // 径向畸变
        T radial_distortion = T(1.0) + distortion[0] * r2 + distortion[1] * r4 + distortion[4] * r6;

        // 切向畸变
        T tangential_x = T(2.0) * distortion[2] * xp * yp + distortion[3] * (r2 + T(2.0) * xp * xp);
        T tangential_y = distortion[2] * (r2 + T(2.0) * yp * yp) + T(2.0) * distortion[3] * xp * yp;

        // 应用畸变
        T xd = xp * radial_distortion + tangential_x;
        T yd = yp * radial_distortion + tangential_y;

        // 投影到像素坐标
        T predicted_x = intrinsics[0] * xd + intrinsics[2];  // fx * xd + cx
        T predicted_y = intrinsics[1] * yd + intrinsics[3];  // fy * yd + cy

        // 计算残差
        residuals[0] = predicted_x - T(observed_x_);
        residuals[1] = predicted_y - T(observed_y_);

        return true;
    }
};

/**
 * @brief Ceres Solver重投影误差代价函数 - 同时优化外参、内参和畸变
 */
class ReprojectionErrorFull {
public:
    ReprojectionErrorFull(double observed_x, double observed_y,
                         double object_point_x, double object_point_y, double object_point_z)
        : observed_x_(observed_x), observed_y_(observed_y)
        , object_point_x_(object_point_x), object_point_y_(object_point_y), object_point_z_(object_point_z) {}

    template <typename T>
    bool operator()(const T* const extrinsics,
                   const T* const intrinsics,
                   const T* const distortion,
                   T* residuals) const {
        return ComputeReprojectionError(extrinsics, intrinsics, distortion, residuals);
    }

private:
    double observed_x_, observed_y_;
    double object_point_x_, object_point_y_, object_point_z_;

    template <typename T>
    bool ComputeReprojectionError(const T* const extrinsics,
                                 const T* const intrinsics,
                                 const T* const distortion,
                                 T* residuals) const {
        // 外参：[rx, ry, rz, tx, ty, tz]
        const T* rotation = extrinsics;
        const T* translation = extrinsics + 3;

        // 3D点坐标
        T object_point[3];
        object_point[0] = T(object_point_x_);
        object_point[1] = T(object_point_y_);
        object_point[2] = T(object_point_z_);

        // 应用旋转和平移变换
        T camera_point[3];
        ceres::AngleAxisRotatePoint(rotation, object_point, camera_point);
        camera_point[0] += translation[0];
        camera_point[1] += translation[1];
        camera_point[2] += translation[2];

        // 投影到归一化图像平面
        T xp = camera_point[0] / camera_point[2];
        T yp = camera_point[1] / camera_point[2];

        // 应用畸变
        T r2 = xp * xp + yp * yp;
        T r4 = r2 * r2;
        T r6 = r4 * r2;

        // 径向畸变
        T radial_distortion = T(1.0) + distortion[0] * r2 + distortion[1] * r4 + distortion[4] * r6;

        // 切向畸变
        T tangential_x = T(2.0) * distortion[2] * xp * yp + distortion[3] * (r2 + T(2.0) * xp * xp);
        T tangential_y = distortion[2] * (r2 + T(2.0) * yp * yp) + T(2.0) * distortion[3] * xp * yp;

        // 应用畸变
        T xd = xp * radial_distortion + tangential_x;
        T yd = yp * radial_distortion + tangential_y;

        // 投影到像素坐标
        T predicted_x = intrinsics[0] * xd + intrinsics[2];
        T predicted_y = intrinsics[1] * yd + intrinsics[3];

        // 计算残差
        residuals[0] = predicted_x - T(observed_x_);
        residuals[1] = predicted_y - T(observed_y_);

        return true;
    }
};

/**
 * @brief Bundle Adjustment优化器类
 */
class CeresBundleAdjuster {
public:
    /**
     * @brief 构造函数
     */
    CeresBundleAdjuster();

    /**
     * @brief 析构函数
     */
    ~CeresBundleAdjuster();

    /**
     * @brief 执行Bundle Adjustment优化
     * @param image_data 图像数据列表
     * @param camera_intrinsics 相机内参
     * @param distortion_coeffs 畸变系数
     * @param params 优化参数
     * @param optimize_intrinsics 是否优化内参
     * @param optimize_distortion 是否优化畸变
     * @return 优化是否成功
     */
    bool optimize(std::vector<core::CalibrationImageData>& image_data,
                 core::CameraIntrinsics& camera_intrinsics,
                 core::DistortionCoefficients& distortion_coeffs,
                 const core::ExtrinsicCalibrationParams& params,
                 bool optimize_intrinsics = false,
                 bool optimize_distortion = false);

    /**
     * @brief 获取优化统计信息
     */
    const ceres::Solver::Summary& getSolverSummary() const { return summary_; }

    /**
     * @brief 设置求解器选项
     */
    void setSolverOptions(const ceres::Solver::Options& options) { solver_options_ = options; }

private:
    ceres::Solver::Options solver_options_;
    ceres::Solver::Summary summary_;

    /**
     * @brief 设置默认求解器选项
     */
    void setDefaultSolverOptions();

    /**
     * @brief 参数化外参数据
     */
    void parameterizeExtrinsics(const std::vector<core::CalibrationImageData>& image_data,
                               std::vector<double>& extrinsics_data);

    /**
     * @brief 从参数化数据更新外参
     */
    void updateExtrinsicsFromParameters(const std::vector<double>& extrinsics_data,
                                       std::vector<core::CalibrationImageData>& image_data);

    /**
     * @brief 参数化内参数据
     */
    void parameterizeIntrinsics(const core::CameraIntrinsics& intrinsics, double* intrinsics_data);

    /**
     * @brief 从参数化数据更新内参
     */
    void updateIntrinsicsFromParameters(const double* intrinsics_data, core::CameraIntrinsics& intrinsics);

    /**
     * @brief 参数化畸变系数数据
     */
    void parameterizeDistortion(const core::DistortionCoefficients& distortion, double* distortion_data);

    /**
     * @brief 从参数化数据更新畸变系数
     */
    void updateDistortionFromParameters(const double* distortion_data, core::DistortionCoefficients& distortion);
};

#else // !USE_CERES_SOLVER

/**
 * @brief 当Ceres Solver不可用时的占位符类
 */
class CeresBundleAdjuster {
public:
    CeresBundleAdjuster() {}
    ~CeresBundleAdjuster() {}

    bool optimize(std::vector<core::CalibrationImageData>& image_data,
                 core::CameraIntrinsics& camera_intrinsics,
                 core::DistortionCoefficients& distortion_coeffs,
                 const core::ExtrinsicCalibrationParams& params,
                 bool optimize_intrinsics = false,
                 bool optimize_distortion = false) {
        // 返回false表示优化不可用
        return false;
    }

    struct DummySummary {
        double initial_cost = 0.0;
        double final_cost = 0.0;
        int num_successful_steps = 0;
        double total_time_in_seconds = 0.0;
    };

    const DummySummary& getSolverSummary() const {
        static DummySummary dummy;
        return dummy;
    }
};

#endif // USE_CERES_SOLVER

} // namespace calibration
} // namespace camera_calibration

#endif // CAMERA_CALIBRATION_CERES_BUNDLE_ADJUSTMENT_H
