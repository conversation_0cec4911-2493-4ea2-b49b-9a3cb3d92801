#ifndef CAMERA_CALIBRATION_CORE_CONFIG_MANAGER_H
#define CAMERA_CALIBRATION_CORE_CONFIG_MANAGER_H

#include "types.h"
#include <yaml-cpp/yaml.h>
#include <string>
#include <memory>
#include <mutex>

namespace camera_calibration {
namespace core {

/**
 * @brief 配置管理器类 - 单例模式
 * 
 * 负责加载和管理所有配置参数，包括：
 * - 主配置文件 (config.yaml)
 * - 相机内参配置 (camera_intrinsics.yaml)
 * - 运行时参数管理
 */
class ConfigManager {
public:
    /**
     * @brief 获取配置管理器单例实例
     */
    static ConfigManager& getInstance();
    
    /**
     * @brief 加载配置文件
     * @param config_path 主配置文件路径
     * @param intrinsics_path 相机内参配置文件路径
     * @return 是否加载成功
     */
    bool loadConfig(const std::string& config_path, const std::string& intrinsics_path);

    /**
     * @brief 加载外参标定配置文件
     * @param extrinsics_config_path 外参标定配置文件路径
     * @return 是否加载成功
     */
    bool loadExtrinsicConfig(const std::string& extrinsics_config_path);
    
    /**
     * @brief 重新加载配置文件
     */
    bool reloadConfig();
    
    /**
     * @brief 保存当前配置到文件
     */
    bool saveConfig(const std::string& config_path = "") const;
    
    // 路径相关配置
    std::string getInputImagePath() const;
    std::string getSavePath() const;
    std::string getDebugPath() const;
    void setInputImagePath(const std::string& path);
    void setSavePath(const std::string& path);
    
    // 相机参数
    CameraIntrinsics getCameraIntrinsics() const;
    DistortionCoefficients getDistortionCoefficients() const;
    void setCameraIntrinsics(const CameraIntrinsics& intrinsics);
    void setDistortionCoefficients(const DistortionCoefficients& dist_coeffs);
    
    // 图像处理参数
    ImageProcessingParams getImageProcessingParams() const;
    void setImageProcessingParams(const ImageProcessingParams& params);
    
    // Blob 检测参数
    BlobDetectorParams getBlobDetectorParams() const;
    void setBlobDetectorParams(const BlobDetectorParams& params);

    // 特征验证参数
    std::vector<int> getLeftColumnCounts() const;
    std::vector<int> getRightColumnCounts() const;
    
    // 棋盘格参数
    cv::Size getChessboardSize() const;
    cv::Rect getChessboardBounds() const;
    int getBottomThreshold() const;
    int getChessYOffset() const;
    
    // 世界坐标系参数
    std::vector<double> getWorldHAxis() const;
    std::vector<double> getWorldWAxis() const;
    int getWorldGridCols() const;
    int getWorldGridRows() const;
    double getDistanceToCamera() const;
    double getDistanceToCameraCenter() const;
    
    // 列点数配置
    std::vector<int> getLeftColumnPointCounts() const;
    std::vector<int> getRightColumnPointCounts() const;

    // 外参标定配置
    ExtrinsicCalibrationParams getExtrinsicCalibrationParams() const;
    void setExtrinsicCalibrationParams(const ExtrinsicCalibrationParams& params);
    CalibrationMode getCalibrationMode() const;
    void setCalibrationMode(CalibrationMode mode);

    // 标定板配置
    CalibrationBoard getCalibrationBoard() const;
    void setCalibrationBoard(const CalibrationBoard& board);

    // 获取原始 YAML 节点（用于扩展）
    const YAML::Node& getMainConfig() const { return main_config_; }
    const YAML::Node& getIntrinsicsConfig() const { return intrinsics_config_; }
    
    // // 调试信息
    // void printConfig() const;
    
private:
    ConfigManager() = default;
    ~ConfigManager() = default;
    
    // 禁用拷贝构造和赋值
    ConfigManager(const ConfigManager&) = delete;
    ConfigManager& operator=(const ConfigManager&) = delete;
    
    // 加载具体配置项
    bool loadMainConfig(const std::string& config_path);
    bool loadIntrinsicsConfig(const std::string& intrinsics_path);
    bool parseImageProcessingParams();
    bool parseBlobDetectorParams();
    bool parseWorldGridParams();
    bool parseExtrinsicCalibrationParams();
    
    // 成员变量
    mutable std::mutex config_mutex_;
    
    YAML::Node main_config_;
    YAML::Node intrinsics_config_;
    
    std::string main_config_path_;
    std::string intrinsics_config_path_;
    
    // 缓存的配置参数
    mutable bool config_loaded_ = false;
    mutable CameraIntrinsics camera_intrinsics_;
    mutable DistortionCoefficients distortion_coeffs_;
    mutable ImageProcessingParams image_processing_params_;
    mutable BlobDetectorParams blob_detector_params_;

    // 路径配置
    mutable std::string input_image_path_;
    mutable std::string save_path_;

    // 世界坐标系配置
    mutable std::vector<double> world_h_axis_;
    mutable std::vector<double> world_w_axis_;
    mutable std::vector<int> left_column_counts_;
    mutable std::vector<int> right_column_counts_;
    mutable int world_grid_cols_ = 14;
    mutable int world_grid_rows_ = 7;
    mutable double distance_to_camera_ = 1.0;
    mutable double distance_to_camera_center_ = 0.0;

    // 外参标定配置
    mutable ExtrinsicCalibrationParams extrinsic_params_;
    mutable CalibrationMode calibration_mode_ = CalibrationMode::FORWARD_MAPPING;
    mutable CalibrationBoard calibration_board_;
};

/**
 * @brief 配置管理器的便捷访问宏
 */
#define CONFIG_MGR camera_calibration::core::ConfigManager::getInstance()

} // namespace core
} // namespace camera_calibration

#endif // CAMERA_CALIBRATION_CORE_CONFIG_MANAGER_H
