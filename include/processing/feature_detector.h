#ifndef CAMERA_CALIBRATION_PROCESSING_FEATURE_DETECTOR_H
#define CAMERA_CALIBRATION_PROCESSING_FEATURE_DETECTOR_H

#include "core/types.h"
#include <opencv2/opencv.hpp>
#include <opencv2/features2d.hpp>
#include <vector>
#include <string>

// 前向声明与原项目一致的数据结构
struct PointInfo;

namespace camera_calibration {
namespace processing {

/**
 * @brief 特征检测器类
 * 
 * 负责检测和标记图像中的特征点，包括：
 * - SimpleBlobDetector 黑点检测
 * - 特征点分类和标记
 * - 特征点验证和过滤
 * - 结果可视化
 */
class FeatureDetector {
public:
    /**
     * @brief 构造函数
     */
    FeatureDetector();
    
    /**
     * @brief 析构函数
     */
    ~FeatureDetector();
    
    /**
     * @brief 初始化特征检测器
     * @param blob_params Blob 检测参数
     * @param processing_params 图像处理参数
     * @param left_column_counts 左侧列点数要求
     * @param right_column_counts 右侧列点数要求
     * @return 初始化是否成功
     */
    bool initialize(const core::BlobDetectorParams& blob_params,
                   const core::ImageProcessingParams& processing_params,
                   const std::vector<int>& left_column_counts = {},
                   const std::vector<int>& right_column_counts = {});
    
    /**
     * @brief 检测图像中的特征点
     * @param input_image 输入图像
     * @param feature_points 输出特征点列表
     * @return 检测是否成功
     */
    bool detectFeatures(const cv::Mat& input_image, 
                       std::vector<core::FeaturePoint>& feature_points, const std::string& output_path);
    

    

    

    
    /**
     * @brief 计算特征点的极坐标
     * @param pixel_coord 像素坐标
     * @param reference_point 参考点
     * @return 极坐标 (距离, 角度)
     */
    cv::Vec2d calculatePolarCoordinates(const core::Point2D& pixel_coord,
                                       const core::Point2D& reference_point);
    
    /**
     * @brief 计算两点间距离
     * @param point1 点1
     * @param point2 点2
     * @return 距离
     */
    double calculateDistance(const core::Point2D& point1, const core::Point2D& point2);
    
    /**
     * @brief 计算两点间的角度
     * @param point1 起始点
     * @param point2 终止点
     * @return 角度（弧度）
     */
    double calculateAngle(const core::Point2D& point1, const core::Point2D& point2);
    
    /**
     * @brief 获取检测统计信息
     */
    struct DetectionStats {
        int total_keypoints_detected;
        int valid_feature_points;
        int left_side_points;
        int right_side_points;
        bool validation_passed;
        std::vector<int> left_column_counts;
        std::vector<int> right_column_counts;
        core::Point2D reference_point;
        std::string error_message;
    };
    
    DetectionStats getLastDetectionStats() const { return last_stats_; }
    
    /**
     * @brief 保存检测结果到 CSV 文件
     * @param feature_points 特征点列表
     * @param csv_filename CSV 文件路径
     * @return 保存是否成功
     */
    bool saveResultsToCSV(const std::vector<core::FeaturePoint>& feature_points,
                         const std::string& csv_filename);
    

    
    /**
     * @brief 设置调试模式
     * @param enable 是否启用调试模式
     */
    void setDebugMode(bool enable) { debug_mode_ = enable; }
    
    /**
     * @brief 获取调试模式状态
     */
    bool isDebugMode() const { return debug_mode_; }
    
private:
    // 分离左右侧特征点
    void separateLeftRightPoints(const std::vector<cv::KeyPoint>& keypoints,
                                std::vector<cv::KeyPoint>& left_points,
                                std::vector<cv::KeyPoint>& right_points,
                                core::Point2D& reference_point);
    
    // 对特征点进行排序和标记
    void sortAndLabelPoints(const std::vector<cv::KeyPoint>& points,
                           std::vector<core::FeaturePoint>& labeled_points,
                           const core::Point2D& reference_point,
                           bool is_left_side);
    
    // 验证列点数
    bool validateColumnCounts(const std::vector<core::FeaturePoint>& feature_points,
                             std::vector<int>& actual_left_counts,
                             std::vector<int>& actual_right_counts);
    
    // 计算图像中心线
    double calculateImageCenterLine(const std::vector<cv::KeyPoint>& keypoints);
    
    // 验证输入参数
    bool validateInputs(const cv::Mat& image);

    // 添加与原项目完全一致的函数声明
    bool LabelthePoint(std::vector<cv::Point>& keypoints_xy, std::vector<struct PointInfo>& result_set,
                      cv::Point& reference_point);
    double getDistance(cv::Point& pointO, cv::Point& pointA);
    double getK(cv::Point& point1, cv::Point& point2);

    // 重构后的子函数声明
    bool calculateReferencePoint(const std::vector<cv::Point>& keypoints_xy, cv::Point& referencepoint);
    bool classifyLeftRightPoints(const std::vector<cv::Point>& keypoints_xy,
                                const cv::Point& referencepoint,
                                std::vector<PointInfo>& LeftSideSet,
                                std::vector<PointInfo>& RightSideSet);
    bool labelLeftSidePoints(std::vector<PointInfo>& LeftSideSet, std::vector<PointInfo>& result_set);
    bool labelRightSidePoints(std::vector<PointInfo>& RightSideSet, std::vector<PointInfo>& result_set);



    // 保存增强图像 (与saveDebugImage保存方式统一)
    void saveImage(const cv::Mat& image, const std::string& filename,
                          const std::string& output_path);
    
    // 成员变量
    bool initialized_;
    bool debug_mode_;

    // 检测参数
    core::BlobDetectorParams blob_params_;
    core::ImageProcessingParams processing_params_;

    // 列点数验证配置
    std::vector<int> points_per_column_left_;
    std::vector<int> points_per_column_right_;

    // OpenCV Blob 检测器
    cv::Ptr<cv::SimpleBlobDetector> blob_detector_;

    // 统计信息
    DetectionStats last_stats_;
    
    // 常量
    static const double MIN_DISTANCE_THRESHOLD;
    static const double MAX_ANGLE_DEVIATION;
    static const int MIN_POINTS_PER_COLUMN;
    static const int MAX_POINTS_PER_COLUMN;
};

} // namespace processing
} // namespace camera_calibration

#endif // CAMERA_CALIBRATION_PROCESSING_FEATURE_DETECTOR_H
