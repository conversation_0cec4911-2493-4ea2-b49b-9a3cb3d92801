#include "processing/feature_detector.h"
#include "utils/common_utils.h"
#include "core/constants.h"
#include <iostream>
#include <fstream>
#include <sstream>
#include <algorithm>
#include <cmath>
#include <numeric>

// 添加与原项目一致的数据结构
struct PointInfo {
    cv::Point coor;     // 像素坐标
    cv::Vec2d poly;     // 极坐标 (距离, 角度)
    cv::Point label;    // 标签 (列号, 行号)
};

namespace camera_calibration {
namespace processing {

// 静态常量定义
const double FeatureDetector::MIN_DISTANCE_THRESHOLD = 10.0;
const double FeatureDetector::MAX_ANGLE_DEVIATION = 0.2;
const int FeatureDetector::MIN_POINTS_PER_COLUMN = 1;
const int FeatureDetector::MAX_POINTS_PER_COLUMN = 10;

FeatureDetector::FeatureDetector()
    : initialized_(false)
    , debug_mode_(false) {
}

FeatureDetector::~FeatureDetector() {
    // 清理资源
}

bool FeatureDetector::initialize(const core::BlobDetectorParams& blob_params,
                                const core::ImageProcessingParams& processing_params,
                                const std::vector<int>& left_column_counts,
                                const std::vector<int>& right_column_counts) {
    try {
        blob_params_ = blob_params;
        processing_params_ = processing_params;
        points_per_column_left_ = left_column_counts;
        points_per_column_right_ = right_column_counts;

        // 创建 OpenCV SimpleBlobDetector
        cv::SimpleBlobDetector::Params params = blob_params.toOpenCVParams();
        blob_detector_ = cv::SimpleBlobDetector::create(params);

        if (!blob_detector_) {
            LOG_ERROR_FUNC("FeatureDetector", "创建 Blob 检测器失败");
            return false;
        }

        initialized_ = true;
        // LOG_INFO_FUNC("FeatureDetector", "特征检测器初始化成功");
        // LOG_INFO_FUNC("FeatureDetector", "左侧列点数要求: " + std::to_string(points_per_column_left_.size()) + " 列");
        // LOG_INFO_FUNC("FeatureDetector", "右侧列点数要求: " + std::to_string(points_per_column_right_.size()) + " 列");
        return true;

    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("FeatureDetector", "特征检测器初始化失败: " + std::string(e.what()));
        return false;
    }
}
void FeatureDetector::saveImage(const cv::Mat& image, const std::string& filename,
                                          const std::string& output_path) {
    if (!debug_mode_) return;

    // 使用统一的图像保存方式
    if (utils::FileOperationHelper::saveImageSafely(image, filename, output_path, "FeatureDetector")) {
        LOG_DEBUG_FUNC("FeatureDetector", "增强图像已保存: " + output_path  + filename, debug_mode_);
    } else {
        LOG_ERROR_FUNC("FeatureDetector", "保存增强图像失败: " + output_path + "/" + filename);
    }
}
bool FeatureDetector::detectFeatures(const cv::Mat& input_image,
                                    std::vector<core::FeaturePoint>& feature_points, const std::string& output_path) {
    if (!initialized_) {
        LOG_ERROR_FUNC("FeatureDetector", "特征检测器未初始化");
        return false;
    }

    if (!utils::ImageValidator::validateImage(input_image, "FeatureDetector")) {
        return false;
    }

    try {
        LOG_INFO_FUNC("FeatureDetector", "开始特征点检测...");

        // 1. 使用 SimpleBlobDetector 检测关键点
        std::vector<cv::KeyPoint> keypoints;
        blob_detector_->detect(input_image, keypoints);

        if (keypoints.empty()) {
            LOG_ERROR_FUNC("FeatureDetector", "未检测到任何特征点");
            last_stats_.total_keypoints_detected = 0;
            last_stats_.validation_passed = false;
            last_stats_.error_message = "未检测到任何特征点";
            return false;
        }

        LOG_INFO_FUNC("FeatureDetector", "检测到 " + std::to_string(keypoints.size()) + " 个原始关键点");
        cv::Mat im_with_keypoints;

        drawKeypoints(input_image, keypoints, im_with_keypoints, cv::Scalar(0, 0, 255),
                  cv::DrawMatchesFlags::DRAW_RICH_KEYPOINTS);
        // // 使用统一的图像保存方式
        // utils::FileOperationHelper::saveImageSafely(im_with_keypoints, "7out_enhanceBolb.bmp", "./", "FeatureDetector");
        saveImage(im_with_keypoints, constants::standard_filenames::BLOB_IMAGE, output_path);

        // 2. 转换KeyPoint为Point格式 (对应原项目的关键步骤)
        std::vector<cv::Point> keypoints_xy;
        for (size_t i = 0; i < keypoints.size(); i++) {
            cv::Point xy = keypoints[i].pt;
            keypoints_xy.push_back(xy);
        }

        // 3. 标记特征点 (对应原项目的 LabelthePoint)
        std::vector<PointInfo> ResultSet;
        cv::Point referencepoint;
        if (!LabelthePoint(keypoints_xy, ResultSet, referencepoint)) {
            LOG_ERROR_FUNC("FeatureDetector", "特征点标记失败");
            return false;
        }

        // 4. 创建可视化图像 (对应原项目的白图 + 标号)
        cv::Mat Keypointview(input_image.rows, input_image.cols, CV_8UC1);
        cv::Mat_<uchar>::iterator itNew = Keypointview.begin<uchar>();
        for (; itNew != Keypointview.end<uchar>(); ++itNew) {
            *itNew = 255;
        }

        // 绘制特征点和标签 (对应原项目的可视化)
        for (const auto& point_info : ResultSet) {
            cv::Point center = point_info.coor;

            // 绘制圆点
            cv::circle(Keypointview, center, 5, cv::Scalar(0), -1);

            // 绘制标签
            std::string label = "(" + std::to_string(point_info.label.x) + "," + std::to_string(point_info.label.y) + ")";
            cv::putText(Keypointview, label, cv::Point(center.x + 10, center.y - 10),
                       cv::FONT_HERSHEY_SIMPLEX, 0.5, cv::Scalar(0), 1);
        }
        saveImage(Keypointview, constants::standard_filenames::SORTED_IMAGE, output_path);

        
        // 5. 对结果检查逐列个数 (对应原项目的验证功能)
        bool generateTable = true;
        bool pointErr = true;

        if (!points_per_column_left_.empty() && !points_per_column_right_.empty()) {
            std::vector<int> ColL(points_per_column_left_.size(), 0);
            std::vector<int> ColR(points_per_column_right_.size(), 0);

            for (const auto& p : ResultSet) {
                if (p.label.x < 0) {
                    int idx = -p.label.x - 1;
                    if (idx >= 0 && idx < static_cast<int>(ColL.size())) {
                        ColL[idx]++;
                    } else {
                        std::cerr << "\nErr1--左图列号非法或越界（label.x = " << p.label.x << "），请检查图像。" << std::endl;
                        generateTable = false;
                        pointErr = false;
                    }
                } else if (p.label.x > 0) {
                    int idx = p.label.x - 1;
                    if (idx >= 0 && idx < static_cast<int>(ColR.size())) {
                        ColR[idx]++;
                    } else {
                        std::cerr << "\nErr1--右图列号非法或越界（label.x = " << p.label.x << "），请检查图像。" << std::endl;
                        generateTable = false;
                        pointErr = false;
                    }
                } else {
                    std::cerr << "\nErr1--列号为 0 是非法值，请检查图像。" << std::endl;
                    generateTable = false;
                    pointErr = false;
                }
            }

            // 校验结果是否匹配
            if (ColL != points_per_column_left_) {
                std::cerr << "\nErr2--左图某列的点数不正确，请检查 leftImage。" << std::endl;
                LOG_ERROR_FUNC("FeatureDetector", "左图列点数验证失败");
                generateTable = false;
                pointErr = false;
            }
            if (ColR != points_per_column_right_) {
                std::cerr << "\nErr2--右图某列的点数不正确，请检查 rightImage。" << std::endl;
                LOG_ERROR_FUNC("FeatureDetector", "右图列点数验证失败");
                generateTable = false;
                pointErr = false;
            }

            if (!pointErr) {
                LOG_ERROR_FUNC("FeatureDetector", "特征点验证失败，存在点数错误");
                last_stats_.validation_passed = false;
                last_stats_.error_message = "特征点验证失败";
                return false;
            }

            if (pointErr && generateTable) {
                LOG_INFO_FUNC("FeatureDetector", "特征点列数验证通过");
            }
        }

        // 6. 转换为 FeaturePoint 格式
        feature_points.clear();
        for (const auto& point_info : ResultSet) {
            core::FeaturePoint feature_point;
            // 确保像素坐标与原项目一致（整数精度）
            feature_point.pixel_coord = core::Point2D(static_cast<double>(point_info.coor.x),
                                                     static_cast<double>(point_info.coor.y));
            feature_point.label = cv::Point(point_info.label.x, point_info.label.y);
            feature_point.polar_coord[0] = point_info.poly[0]; // distance
            feature_point.polar_coord[1] = point_info.poly[1]; // angle
            feature_point.area = 1.0; // 简化处理
            feature_point.circularity = 1.0; // 简化处理
            feature_points.push_back(feature_point);
        }
        
        // 6. 更新统计信息
        // last_stats_.total_keypoints_detected = static_cast<int>(keypoints.size());
        // last_stats_.reference_point = core::Point2D(referencepoint.x, referencepoint.y);
        // last_stats_.left_side_points = LN;
        // last_stats_.right_side_points = RN;
        // last_stats_.validation_passed = true;
        // last_stats_.error_message = "特征检测成功";

        LOG_INFO_FUNC("FeatureDetector", "特征点检测完成，有效点数: " + std::to_string(feature_points.size()));
        return true;

    } catch (const cv::Exception& e) {
        LOG_ERROR_FUNC("FeatureDetector", "OpenCV 异常: " + std::string(e.what()));
        last_stats_.validation_passed = false;
        last_stats_.error_message = "OpenCV 异常: " + std::string(e.what());
        return false;
    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("FeatureDetector", "特征检测异常: " + std::string(e.what()));
        last_stats_.validation_passed = false;
        last_stats_.error_message = "特征检测异常: " + std::string(e.what());
        return false;
    }
}








cv::Vec2d FeatureDetector::calculatePolarCoordinates(const core::Point2D& pixel_coord,
                                                    const core::Point2D& reference_point) {
    double dx = pixel_coord.x - reference_point.x;
    double dy = pixel_coord.y - reference_point.y;
    
    double distance = std::sqrt(dx * dx + dy * dy);
    double angle = std::atan2(dy, dx);
    
    return cv::Vec2d(distance, angle);
}

double FeatureDetector::calculateDistance(const core::Point2D& point1, const core::Point2D& point2) {
    double dx = point1.x - point2.x;
    double dy = point1.y - point2.y;
    return std::sqrt(dx * dx + dy * dy);
}

double FeatureDetector::calculateAngle(const core::Point2D& point1, const core::Point2D& point2) {
    double dx = point2.x - point1.x;
    double dy = point2.y - point1.y;
    return std::atan2(dy, dx);
}

bool FeatureDetector::saveResultsToCSV(const std::vector<core::FeaturePoint>& feature_points,
                                       const std::string& csv_filename) {
    try {
        std::ofstream file(csv_filename);
        if (!file.is_open()) {
            LOG_ERROR_FUNC("FeatureDetector", "无法创建 CSV 文件: " + csv_filename);
            return false;
        }
        
        // 写入标题行
        file << "label_x,label_y,pixel_x,pixel_y,distance,angle,area,circularity\n";
        
        // 写入数据行
        for (const auto& point : feature_points) {
            file << point.label.x << "," << point.label.y << ","
                 << point.pixel_coord.x << "," << point.pixel_coord.y << ","
                 << point.polar_coord[0] << "," << point.polar_coord[1] << ","
                 << point.area << "," << point.circularity << "\n";
        }
        
        file.close();
        LOG_INFO_FUNC("FeatureDetector", "特征点数据已保存到: " + csv_filename);
        return true;
        
    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("FeatureDetector", "保存 CSV 文件异常: " + std::string(e.what()));
        return false;
    }
}



// 私有方法实现
void FeatureDetector::separateLeftRightPoints(const std::vector<cv::KeyPoint>& keypoints,
                                             std::vector<cv::KeyPoint>& left_points,
                                             std::vector<cv::KeyPoint>& right_points,
                                             core::Point2D& reference_point) {
    left_points.clear();
    right_points.clear();
    
    for (const auto& kp : keypoints) {
        if (kp.pt.x < reference_point.x) {
            left_points.push_back(kp);
        } else {
            right_points.push_back(kp);
        }
    }
}

void FeatureDetector::sortAndLabelPoints(const std::vector<cv::KeyPoint>& points,
                                        std::vector<core::FeaturePoint>& labeled_points,
                                        const core::Point2D& reference_point,
                                        bool is_left_side) {
    labeled_points.clear();
    
    // 按 Y 坐标排序（从上到下）
    std::vector<cv::KeyPoint> sorted_points = points;
    std::sort(sorted_points.begin(), sorted_points.end(),
              [](const cv::KeyPoint& a, const cv::KeyPoint& b) {
                  return a.pt.y < b.pt.y;
              });
    
    // 为每个点分配标签
    for (size_t i = 0; i < sorted_points.size(); ++i) {
        core::FeaturePoint feature_point;
        feature_point.pixel_coord = core::Point2D::fromCvPoint(sorted_points[i].pt);
        feature_point.area = M_PI * sorted_points[i].size * sorted_points[i].size / 4.0;
        feature_point.circularity = 1.0; // 简化处理
        
        // 计算极坐标
        feature_point.polar_coord = calculatePolarCoordinates(feature_point.pixel_coord, reference_point);
        
        // 分配标签（简化的标记方式）
        if (is_left_side) {
            feature_point.label.x = -static_cast<int>(i / 7) - 1; // 左侧为负数
        } else {
            feature_point.label.x = static_cast<int>(i / 7) + 1;  // 右侧为正数
        }
        feature_point.label.y = static_cast<int>(i % 7) + 1;
        
        labeled_points.push_back(feature_point);
    }
}

bool FeatureDetector::validateColumnCounts(const std::vector<core::FeaturePoint>& feature_points,
                                          std::vector<int>& actual_left_counts,
                                          std::vector<int>& actual_right_counts) {
    // 简化的验证实现
    actual_left_counts.clear();
    actual_right_counts.clear();
    
    // 统计实际的列点数
    std::map<int, int> left_count_map, right_count_map;
    
    for (const auto& point : feature_points) {
        if (point.label.x < 0) {
            left_count_map[point.label.y]++;
        } else {
            right_count_map[point.label.y]++;
        }
    }
    
    // 转换为向量
    for (const auto& pair : left_count_map) {
        actual_left_counts.push_back(pair.second);
    }
    for (const auto& pair : right_count_map) {
        actual_right_counts.push_back(pair.second);
    }
    
    return true; // 简化验证，总是返回 true
}

double FeatureDetector::calculateImageCenterLine(const std::vector<cv::KeyPoint>& keypoints) {
    if (keypoints.empty()) {
        return processing_params_.src_width / 2.0;
    }
    
    // 计算所有关键点的 X 坐标平均值作为中心线
    double sum_x = 0.0;
    for (const auto& kp : keypoints) {
        sum_x += kp.pt.x;
    }
    
    return sum_x / keypoints.size();
}

bool FeatureDetector::validateInputs(const cv::Mat& image) {
    return utils::ImageValidator::validateImage(image, "FeatureDetector");
}

// 重构后的 LabelthePoint 函数 - 主控制函数
bool FeatureDetector::LabelthePoint(std::vector<cv::Point>& keypoints_xy, std::vector<struct PointInfo>& result_set,
                                   cv::Point& referencepoint) {
    try {
        // 1. 计算参考点
        if (!calculateReferencePoint(keypoints_xy, referencepoint)) {
            LOG_ERROR_FUNC("FeatureDetector", "计算参考点失败");
            return false;
        }

        // 2. 分类左右点集
        std::vector<PointInfo> LeftSideSet, RightSideSet;
        if (!classifyLeftRightPoints(keypoints_xy, referencepoint, LeftSideSet, RightSideSet)) {
            LOG_ERROR_FUNC("FeatureDetector", "分类左右点集失败");
            return false;
        }

        // 3. 标记左侧点
        if (!labelLeftSidePoints(LeftSideSet, result_set)) {
            LOG_ERROR_FUNC("FeatureDetector", "标记左侧点失败");
            return false;
        }

        // 4. 标记右侧点
        if (!labelRightSidePoints(RightSideSet, result_set)) {
            LOG_ERROR_FUNC("FeatureDetector", "标记右侧点失败");
            return false;
        }

        LOG_INFO_FUNC("FeatureDetector", "特征点标记完成，左侧: " + std::to_string(LeftSideSet.size()) +
                     " 个，右侧: " + std::to_string(RightSideSet.size()) + " 个");
        return true;

    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("FeatureDetector", "特征点标记异常: " + std::string(e.what()));
        return false;
    }
}




// 实现与原项目完全一致的 getDistance 函数
double FeatureDetector::getDistance(cv::Point& pointO, cv::Point& pointA) {
    double distance;
    distance = pow((pointO.x - pointA.x), 2) + powf((pointO.y - pointA.y), 2);
    distance = sqrt(distance);
    return distance;
}

// 实现与原项目完全一致的 getK 函数
double FeatureDetector::getK(cv::Point& point1, cv::Point& point2) {
    double k;
    // 取负因为像素空间y轴向下
    k = (double) -(point1.y - point2.y) / (point1.x - point2.x);
    return k;
}

// 重构后的子函数实现

bool FeatureDetector::calculateReferencePoint(const std::vector<cv::Point>& keypoints_xy, cv::Point& referencepoint) {
    if (keypoints_xy.empty()) {
        LOG_ERROR_FUNC("FeatureDetector", "关键点列表为空");
        return false;
    }

    try {
        // 计算所有识别出的点的x坐标平均值，以此为中心线切割左右半区
        cv::Point sum = std::accumulate(keypoints_xy.begin(), keypoints_xy.end(), cv::Point(0, 0));
        float mean = static_cast<float>(sum.x) / static_cast<float>(keypoints_xy.size());

        // 分离左右点
        std::vector<cv::Point> LeftPoints, RightPoints;
        for (const auto& p : keypoints_xy) {
            if (p.x < mean) {
                LeftPoints.push_back(p);
            } else {
                RightPoints.push_back(p);
            }
        }

        if (LeftPoints.empty() || RightPoints.empty()) {
            LOG_ERROR_FUNC("FeatureDetector", "左侧或右侧点集为空");
            return false;
        }

        // 找到左侧最右点和右侧最左点
        auto tempL = std::max_element(LeftPoints.begin(), LeftPoints.end(),
                                     [](const cv::Point& a, const cv::Point& b) { return a.x < b.x; });
        auto tempR = std::min_element(RightPoints.begin(), RightPoints.end(),
                                     [](const cv::Point& a, const cv::Point& b) { return a.x < b.x; });

        // 计算参考点
        referencepoint.x = (tempL->x + tempR->x) / 2;
        referencepoint.y = (tempL->y + tempR->y) / 2;


        return true;

    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("FeatureDetector", "计算参考点异常: " + std::string(e.what()));
        return false;
    }
}

bool FeatureDetector::classifyLeftRightPoints(const std::vector<cv::Point>& keypoints_xy,
                                             const cv::Point& referencepoint,
                                             std::vector<PointInfo>& LeftSideSet,
                                             std::vector<PointInfo>& RightSideSet) {
    try {
        LeftSideSet.clear();
        RightSideSet.clear();

        for (const auto& p : keypoints_xy) {
            PointInfo keypoint_info;
            keypoint_info.coor = p;
            cv::Point ref_copy = referencepoint;
            cv::Point p_copy = p;
            keypoint_info.poly[0] = getDistance(ref_copy, p_copy);
            keypoint_info.poly[1] = getK(ref_copy, p_copy);

            if (p.x < referencepoint.x) {
                LeftSideSet.push_back(keypoint_info);
            } else {
                RightSideSet.push_back(keypoint_info);
            }
        }

        LOG_INFO_FUNC("FeatureDetector", "点集分类完成，左侧: " + std::to_string(LeftSideSet.size()) +
                      " 个，右侧: " + std::to_string(RightSideSet.size()) + " 个");
        return true;

    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("FeatureDetector", "分类左右点集异常: " + std::string(e.what()));
        return false;
    }
}

bool FeatureDetector::labelLeftSidePoints(std::vector<PointInfo>& LeftSideSet, std::vector<PointInfo>& result_set) {
    try {
        using namespace camera_calibration::constants;

        int LeftPointNum = LeftSideSet.size();

        // 对左半边进行操作
        for (int i = 0; LeftPointNum > 0; i++) {
            // 找最近点（-1,1）
            auto mml = std::max_element(LeftSideSet.begin(), LeftSideSet.end(),
                                       [](const PointInfo& a, const PointInfo& b) { return a.coor.x < b.coor.x; });

            if (mml == LeftSideSet.end()) break;

            int temply = mml->coor.y;
            mml->label.x = -i - 1;
            mml->label.y = 1;
            result_set.push_back(*mml);
            double K1 = mml->poly[1];
            LeftSideSet.erase(mml);
            LeftPointNum--;

            // 对列进行标注
            int LeftPointNum2 = LeftSideSet.size();

            for (int j = 0; LeftPointNum2 > 0; j++) {
                std::vector<PointInfo> PoolForTestL;

                // 收集大于K的点
                for (const auto& p : LeftSideSet) {
                    if (p.poly[1] >= K1) {
                        PoolForTestL.push_back(p);
                    }
                }

                if (PoolForTestL.empty()) break;

                // 在其中寻找最近点
                auto mmr1 = std::max_element(PoolForTestL.begin(), PoolForTestL.end(),
                                           [](const PointInfo& a, const PointInfo& b) { return a.coor.x < b.coor.x; });

                int temply1 = mmr1->coor.y;

                if (std::abs(temply1 - temply) < POINT_DISTANCE_THRESHOLD) {
                    PoolForTestL.erase(mmr1);
                    if (PoolForTestL.empty()) break;
                    mmr1 = std::max_element(PoolForTestL.begin(), PoolForTestL.end(),
                                          [](const PointInfo& a, const PointInfo& b) { return a.coor.x < b.coor.x; });
                }

                if (mmr1 != PoolForTestL.end()) {
                    PointInfo selected_point = *mmr1;
                    selected_point.label.x = -i - 1;
                    selected_point.label.y = 2 + j;
                    result_set.push_back(selected_point);
                    K1 = selected_point.poly[1];

                    // 从原集合中移除选中的点
                    LeftSideSet.erase(std::remove_if(LeftSideSet.begin(), LeftSideSet.end(),
                                                    [&selected_point](const PointInfo& p) {
                                                        return p.coor == selected_point.coor;
                                                    }), LeftSideSet.end());

                    LeftPointNum2--;
                    LeftPointNum--;
                } else {
                    break;
                }
            }
        }
        LOG_INFO_FUNC("FeatureDetector", "左侧点标记完成，共处理 " + std::to_string(result_set.size()) + " 个点");
        return true;

    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("FeatureDetector", "标记左侧点异常: " + std::string(e.what()));
        return false;
    }
}

bool FeatureDetector::labelRightSidePoints(std::vector<PointInfo>& RightSideSet, std::vector<PointInfo>& result_set) {
    try {
        using namespace camera_calibration::constants;

        int RightPointNum = RightSideSet.size();

        // 对右半边进行操作
        for (int i = 0; RightPointNum > 0; i++) {
            // 找最近点（1,1）
            auto mmz = std::min_element(RightSideSet.begin(), RightSideSet.end(),
                                       [](const PointInfo& a, const PointInfo& b) { return a.coor.x < b.coor.x; });

            if (mmz == RightSideSet.end()) break;

            int tempry = mmz->coor.y;
            mmz->label.x = i + 1;
            mmz->label.y = 1;
            result_set.push_back(*mmz);
            double K2 = mmz->poly[1];
            RightSideSet.erase(mmz);
            RightPointNum--;

            // 对列进行标注
            int RightPointNum2 = RightSideSet.size();

            for (int j = 0; RightPointNum2 > 0; j++) {
                std::vector<PointInfo> PoolForTestR;

                // 收集小于K的点
                for (const auto& p : RightSideSet) {
                    if (p.poly[1] <= K2) {
                        PoolForTestR.push_back(p);
                    }
                }

                if (PoolForTestR.empty()) break;

                // 在其中寻找最近点
                auto mmk1 = std::min_element(PoolForTestR.begin(), PoolForTestR.end(),
                                           [](const PointInfo& a, const PointInfo& b) { return a.coor.x < b.coor.x; });

                int tempry1 = mmk1->coor.y;

                if (std::abs(tempry1 - tempry) < POINT_DISTANCE_THRESHOLD) {
                    PoolForTestR.erase(mmk1);
                    if (PoolForTestR.empty()) break;
                    mmk1 = std::min_element(PoolForTestR.begin(), PoolForTestR.end(),
                                          [](const PointInfo& a, const PointInfo& b) { return a.coor.x < b.coor.x; });
                }

                if (mmk1 != PoolForTestR.end()) {
                    PointInfo selected_point = *mmk1;
                    selected_point.label.x = i + 1;
                    selected_point.label.y = 2 + j;
                    result_set.push_back(selected_point);
                    K2 = selected_point.poly[1];

                    // 从原集合中移除选中的点
                    RightSideSet.erase(std::remove_if(RightSideSet.begin(), RightSideSet.end(),
                                                     [&selected_point](const PointInfo& p) {
                                                         return p.coor == selected_point.coor;
                                                     }), RightSideSet.end());

                    RightPointNum2--;
                    RightPointNum--;
                } else {
                    break;
                }
            }
        }

        LOG_INFO_FUNC("FeatureDetector", "右侧点标记完成，共处理 " + std::to_string(result_set.size()/2) + " 个点");
        return true;

    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("FeatureDetector", "标记右侧点异常: " + std::string(e.what()));
        return false;
    }
}

} // namespace processing
} // namespace camera_calibration
