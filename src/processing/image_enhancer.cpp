#include "processing/image_enhancer.h"
#include "processing/msrcr.h"
#include "utils/file_utils.h"
#include "utils/common_utils.h"
#include "utils/opencv_utils.h"
#include "core/constants.h"
#include <iostream>

namespace camera_calibration {
namespace processing {

// 静态常量定义
const int ImageEnhancer::MIN_GROUND_DETECTION_THRESHOLD = 100;
const int ImageEnhancer::DEFAULT_BORDER_WIDTH = 50;
const double ImageEnhancer::DEFAULT_SCALE_FACTOR = 2.0;

ImageEnhancer::ImageEnhancer()
    : initialized_(false)
    , debug_mode_(false)
    , default_weights_{0.1, 0.1, 0.1}
    , default_sigmas_{30.0, 150.0, 300.0}
    , default_gain_(128.0)
    , default_offset_(128.0) {
}

ImageEnhancer::~ImageEnhancer() {
    // 清理资源
}

bool ImageEnhancer::initialize(const core::ImageProcessingParams& processing_params) {
    try {
        processing_params_ = processing_params;
        initialized_ = true;
        LOG_INFO_FUNC("ImageEnhancer", "图像增强器初始化成功");
        return true;

    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("ImageEnhancer", "图像增强器初始化失败: " + std::string(e.what()));
        return false;
    }
}

bool ImageEnhancer::enhanceImage(const cv::Mat& input_image, cv::Mat& enhanced_image) {
    if (!initialized_) {
        LOG_ERROR_FUNC("ImageEnhancer", "图像增强器未初始化");
        return false;
    }

    if (!utils::ImageValidator::validateImage(input_image, "ImageEnhancer")) {
        return false;
    }

    try {
        // LOG_INFO_FUNC("ImageEnhancer", "开始图像增强处理...");

        // 1. MSRCR 多尺度视网膜增强
        cv::Mat msrcr_image;
        if (!applyMSRCR(input_image, msrcr_image)) {
            LOG_ERROR_FUNC("ImageEnhancer", "MSRCR 增强失败");
            return false;
        }
        
        // if (debug_mode_) {
        //     saveIntermediateResult(msrcr_image, "msrcr_enhanced.bmp", "debug/");
        // }
        
        // 2. 地面区域检测和增强
        cv::Mat ground_enhanced;
        int ground_start_row = findGroundStartRow(msrcr_image);
        if (ground_start_row > 0) {
            if (!enhanceGroundRegion(msrcr_image, ground_enhanced, ground_start_row)) {
                LOG_ERROR_FUNC("ImageEnhancer", "地面区域增强失败");
                ground_enhanced = msrcr_image.clone();
            }
            last_stats_.ground_region_detected = true;
            last_stats_.ground_start_row = ground_start_row;
        } else {
            ground_enhanced = msrcr_image.clone();
            last_stats_.ground_region_detected = false;
            last_stats_.ground_start_row = -1;
        }
        
        // 4. 形态学操作
        cv::Mat morphed_image;
        if (!applyMorphologyOperation(ground_enhanced, morphed_image)) {
            LOG_ERROR_FUNC("ImageEnhancer", "形态学操作失败");
            morphed_image = ground_enhanced.clone();
        }

        // enhanced_image = final_image;
        enhanced_image = morphed_image;

        // 计算统计信息（内联实现）
        cv::Scalar mean_before, stddev_before;
        cv::meanStdDev(input_image, mean_before, stddev_before);
        last_stats_.mean_intensity_before = mean_before[0];

        cv::Scalar mean_after, stddev_after;
        cv::meanStdDev(enhanced_image, mean_after, stddev_after);
        last_stats_.mean_intensity_after = mean_after[0];
        last_stats_.contrast_improvement = last_stats_.mean_intensity_after - last_stats_.mean_intensity_before;
        last_stats_.processed_size = enhanced_image.size();
        last_stats_.status_message = "图像增强完成";

        LOG_INFO_FUNC("ImageEnhancer", "图像增强处理完成");
        return true;

    } catch (const cv::Exception& e) {
        LOG_ERROR_FUNC("ImageEnhancer", "OpenCV 异常: " + std::string(e.what()));
        last_stats_.status_message = "OpenCV 异常: " + std::string(e.what());
        return false;
    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("ImageEnhancer", "图像增强异常: " + std::string(e.what()));
        last_stats_.status_message = "图像增强异常: " + std::string(e.what());
        return false;
    }
}

bool ImageEnhancer::applyMSRCR(const cv::Mat& input_image, cv::Mat& enhanced_image,
                               const std::vector<double>& weights,
                               const std::vector<double>& sigmas,
                               double gain, double offset,
                               double restoration_factor, double color_gain) {
    try {
        LOG_INFO_FUNC("ImageEnhancer", "使用MSRCR算法增强图像...");

        // 设置MSRCR参数，与原项目完全一致
        std::vector<double> use_weights;
        std::vector<double> use_sigmas;

        // 如果没有提供参数，使用与原项目一致的默认值
        if (weights.empty()) {
            // 权重设置：原项目中每个权重为 1./10
            for (int i = 0; i < constants::DEFAULT_MSRCR_SCALES; i++) {
                use_weights.push_back(constants::DEFAULT_WEIGHTS[i]);
            }
        } else {
            use_weights = weights;
        }

        if (sigmas.empty()) {
            // 标准偏差设置：原项目中的值
            for (int i = 0; i < constants::DEFAULT_MSRCR_SCALES; i++) {
                use_sigmas.push_back(constants::DEFAULT_SIGMAS[i]);
            }
        } else {
            use_sigmas = sigmas;
        }

        // 创建Msrcr对象（与原项目类名一致）
        processing::Msrcr msrcr;

        // 执行MSRCR算法，函数名和参数与原项目完全一致：
        // msrcr.MultiScaleRetinexCR(src1, src_enhance, weight, sigema, 128, 128, restoration_factor, color_gain);
        double use_gain = (gain < 0) ? constants::DEFAULT_MSRCR_GAIN : gain;
        double use_offset = (offset < 0) ? constants::DEFAULT_MSRCR_OFFSET : offset;
        double use_restoration_factor = (restoration_factor < 0) ? constants::DEFAULT_RESTORATION_FACTOR : restoration_factor;
        double use_color_gain = (color_gain < 0) ? constants::DEFAULT_COLOR_GAIN : color_gain;

        msrcr.MultiScaleRetinexCR(input_image, enhanced_image, use_weights, use_sigmas,
                                 static_cast<int>(use_gain), static_cast<int>(use_offset),
                                 use_restoration_factor, use_color_gain);

        LOG_INFO_FUNC("ImageEnhancer", "MSRCR图像增强完成");
        return true;

    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("ImageEnhancer", "MSRCR图像增强异常: " + std::string(e.what()));
        return false;
    }
}

int ImageEnhancer::findGroundStartRow(const cv::Mat& groundPics) {
    try {
        cv::Mat groundPicTemp;
        int groundRow=-1;
        cvtColor(groundPics, groundPicTemp, cv::COLOR_BGR2GRAY);
        cv::normalize(groundPicTemp,groundPicTemp,0,255,cv::NORM_MINMAX);
        //float rowSum[groundPicTemp.rows]={};
        float*rowSum=new float[groundPicTemp.rows]();
        for (int i = 0; i < groundPicTemp.rows; ++i) {
            unsigned char* data=groundPicTemp.ptr<uchar>(i);
            for (int j = 0; j < groundPicTemp.cols; ++j) {
                rowSum[i]+=data[j];
            }
        }
        for (int i = 0; i < groundPicTemp.rows; ++i) {
            rowSum[i]=rowSum[i]/groundPicTemp.cols;
        }
        for (int k = 0; k <groundPicTemp.rows-1 ; ++k) {
            int temp=abs(rowSum[k+1]-rowSum[k]);
            if (temp>15){
                groundRow = k+1;
                return groundRow;
            }
        }
        delete[] rowSum;
        if (groundRow==-1)
            LOG_INFO_FUNC("ImageEnhancer", "未检测到明显的地面区域");
        return groundRow;
    
    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("ImageEnhancer", "地面检测异常: " + std::string(e.what()));
        return -1;
    }
}

bool ImageEnhancer::enhanceGroundRegion(const cv::Mat& input_image, cv::Mat& enhanced_image, int ground_start_row, int threshold) {
    try {
        // 实现与原项目 enlargeGroundPic 完全一致的算法
        int rows = input_image.rows;
        int cols = input_image.cols;
        cv::Mat enLargePics, groundPicsGray;

        // 转换为灰度图像
        cv::cvtColor(input_image, groundPicsGray, cv::COLOR_BGR2GRAY);

        // 创建白色背景图像
        enLargePics = constants::WHITE_PIXEL_VALUE * cv::Mat::ones(rows, cols, CV_8U);

        // 查找地面起始行
        // int row_temp = findGroundStartRow(input_image);
        if (ground_start_row == -1) {
            LOG_ERROR_FUNC("ImageEnhancer", "没有找到地面图像的起始行");
            enhanced_image = input_image.clone();
            return false;
        }

        // 提取地面部分并归一化
        cv::Mat partGround = groundPicsGray(cv::Range(ground_start_row, input_image.rows), cv::Range::all());
        cv::normalize(partGround, partGround, 0, 255, cv::NORM_MINMAX);

        // 将归一化的地面部分复制到增强图像
        partGround.copyTo(enLargePics.rowRange(ground_start_row, input_image.rows));

        // 进行部分漂白 (对应原项目的边缘漂白)
        cv::Mat colTemp = constants::WHITE_PIXEL_VALUE * cv::Mat::ones(rows, constants::DEFAULT_BORDER_WIDTH, CV_8U);
        cv::Mat rowTemp = constants::WHITE_PIXEL_VALUE * cv::Mat::ones(constants::DEFAULT_BORDER_HEIGHT, cols, CV_8U);
        colTemp.copyTo(enLargePics.colRange(0, constants::DEFAULT_BORDER_WIDTH));
        colTemp.copyTo(enLargePics.colRange(cols - constants::DEFAULT_BORDER_WIDTH, cols));
        rowTemp.copyTo(enLargePics.rowRange(rows - constants::DEFAULT_BORDER_HEIGHT, rows));
        
        // 黑色区域加深处理 (对应原项目的 dark_thresh 处理)
        cv::Mat darkened = enLargePics.clone();
        int dark_thresh = processing_params_.dark_threshold;  // 使用配置文件中的dark_threshold值
        int darken_amount = processing_params_.darken_amount; // 使用配置文件中的darken_amount值

        LOG_INFO_FUNC("ImageEnhancer", "黑色区域加深处理 - 阈值: " + std::to_string(dark_thresh) +
                     ", 加深程度: " + std::to_string(darken_amount));

        for (int i = 0; i < enLargePics.rows; ++i) {
            for (int j = 0; j < enLargePics.cols; ++j) {
                uchar pixel = enLargePics.at<uchar>(i, j);
                if (pixel < dark_thresh) {
                    // 加深黑色区域（让它变得更黑，但不小于0）
                    darkened.at<uchar>(i, j) = std::max(0, pixel - processing_params_.darken_amount);
                }
            }
        }
        saveIntermediateResult(darkened, "darkened_enLargePics.bmp", "./");
        // 二值化处理
        cv::threshold(darkened, enhanced_image, threshold, 255, cv::THRESH_BINARY);
        // 保存中间结果 (对应原项目的 imwrite("./7out_enhance.bmp",darkened))
        // saveIntermediateResult(darkened, constants::standard_filenames::ENHANCED_IMAGE,);

        LOG_INFO_FUNC("ImageEnhancer", "地面区域增强完成");
        return true;

    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("ImageEnhancer", "地面区域增强异常: " + std::string(e.what()));
        return false;
    }
}



bool ImageEnhancer::applyMorphologyOperation(const cv::Mat& input_image, cv::Mat& output_image) {
    try {
        cv::resize(input_image,output_image,cv::Size(2*input_image.cols,2*input_image.rows));
        threshold(output_image, output_image, 254, constants::WHITE_PIXEL_VALUE, cv::THRESH_BINARY);
        cv::Mat ele = utils::OpenCVUtils::createMorphologyKernel(constants::DEFAULT_KERNEL_SIZE);
        dilate(output_image, output_image, ele, cv::Point(-1, -1), constants::DEFAULT_DILATE_ITERATIONS);
        erode(output_image, output_image, ele, cv::Point(-1, -1), constants::DEFAULT_ERODE_ITERATIONS);

        // // if (debug_mode_) {
        //     saveIntermediateResult(output_image, "src2.bmp", "debug/");
        // // }
        //图像外围一圈像素强行取255,保证贴边的斑块能被BlobDetector识别出来
        for (int row = 0; row < output_image.rows; row++) {
            output_image.at<uchar>(row, output_image.cols - 1) = constants::WHITE_PIXEL_VALUE;
            output_image.at<uchar>(row, 0) = constants::WHITE_PIXEL_VALUE;
        }
        for (int col = 0; col < output_image.cols; col++) {
            output_image.at<uchar>(output_image.rows - 1, col) = constants::WHITE_PIXEL_VALUE;
            output_image.at<uchar>(0, col) = constants::WHITE_PIXEL_VALUE;
        }
        

        LOG_INFO_FUNC("ImageEnhancer", "形态学操作完成");
        return true;
        
    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("ImageEnhancer", "形态学操作异常: " + std::string(e.what()));
        return false;
    }
}





// 私有方法实现

bool ImageEnhancer::validateImage(const cv::Mat& image) {
    if (image.empty()) {
        LOG_ERROR_FUNC("ImageEnhancer", "图像为空");
        return false;
    }
    
    if (image.cols <= 0 || image.rows <= 0) {
        LOG_ERROR_FUNC("ImageEnhancer", "图像尺寸无效");
        return false;
    }
    
    if (image.channels() != 1 && image.channels() != 3) {
        LOG_ERROR_FUNC("ImageEnhancer", "不支持的图像通道数: " + std::to_string(image.channels()));
        return false;
    }
    
    return true;
}

void ImageEnhancer::saveIntermediateResult(const cv::Mat& image, const std::string& filename,
                                          const std::string& output_path) {
    if (!debug_mode_) return;
    utils::FileOperationHelper::saveImageSafely(image, filename, output_path, "ImageEnhancer");
}

} // namespace processing
} // namespace camera_calibration
