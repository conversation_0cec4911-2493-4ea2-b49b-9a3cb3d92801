#include "calibration/ceres_bundle_adjustment.h"
#include "utils/common_utils.h"
#include <iostream>
#include <thread>

namespace camera_calibration {
namespace calibration {

#ifdef USE_CERES_SOLVER

CeresBundleAdjuster::CeresBundleAdjuster() {
    setDefaultSolverOptions();
}

CeresBundleAdjuster::~CeresBundleAdjuster() {
}

void CeresBundleAdjuster::setDefaultSolverOptions() {
    solver_options_.linear_solver_type = ceres::SPARSE_SCHUR;
    solver_options_.minimizer_progress_to_stdout = false;
    solver_options_.max_num_iterations = 100;
    solver_options_.function_tolerance = 1e-6;
    solver_options_.parameter_tolerance = 1e-8;
    solver_options_.gradient_tolerance = 1e-10;
    solver_options_.num_threads = std::thread::hardware_concurrency();
    solver_options_.use_nonmonotonic_steps = true;
}

bool CeresBundleAdjuster::optimize(std::vector<core::CalibrationImageData>& image_data,
                                  core::CameraIntrinsics& camera_intrinsics,
                                  core::DistortionCoefficients& distortion_coeffs,
                                  const core::ExtrinsicCalibrationParams& params,
                                  bool optimize_intrinsics,
                                  bool optimize_distortion) {
    try {
        // 收集有效的图像数据
        std::vector<size_t> valid_indices;
        for (size_t i = 0; i < image_data.size(); ++i) {
            if (image_data[i].corners_found && !image_data[i].image_points.empty()) {
                valid_indices.push_back(i);
            }
        }

        if (valid_indices.size() < 2) {
            LOG_ERROR_FUNC("CeresBundleAdjuster", "有效图像数量不足，需要至少2张图像");
            return false;
        }

        // 参数化外参数据 (每张图像6个参数: rx, ry, rz, tx, ty, tz)
        std::vector<double> extrinsics_data(valid_indices.size() * 6);
        parameterizeExtrinsics(image_data, extrinsics_data);

        // 参数化内参数据 (4个参数: fx, fy, cx, cy)
        double intrinsics_data[4];
        parameterizeIntrinsics(camera_intrinsics, intrinsics_data);

        // 参数化畸变系数数据 (5个参数: k1, k2, p1, p2, k3)
        double distortion_data[5];
        parameterizeDistortion(distortion_coeffs, distortion_data);

        // 创建问题
        ceres::Problem problem;

        // 添加残差块
        for (size_t img_idx = 0; img_idx < valid_indices.size(); ++img_idx) {
            const auto& data = image_data[valid_indices[img_idx]];
            double* extrinsics_ptr = &extrinsics_data[img_idx * 6];

            for (size_t pt_idx = 0; pt_idx < data.image_points.size(); ++pt_idx) {
                const cv::Point2f& image_point = data.image_points[pt_idx];
                const cv::Point3f& object_point = data.object_points[pt_idx];

                ceres::CostFunction* cost_function = nullptr;

                if (optimize_intrinsics || optimize_distortion) {
                    // 同时优化外参、内参和/或畸变
                    cost_function = new ceres::AutoDiffCostFunction<ReprojectionErrorFull, 2, 6, 4, 5>(
                        new ReprojectionErrorFull(image_point.x, image_point.y,
                                                 object_point.x, object_point.y, object_point.z));

                    problem.AddResidualBlock(cost_function, nullptr,
                                           extrinsics_ptr, intrinsics_data, distortion_data);
                } else {
                    // 仅优化外参
                    cost_function = new ceres::AutoDiffCostFunction<ReprojectionErrorExtrinsicOnly, 2, 6>(
                        new ReprojectionErrorExtrinsicOnly(image_point.x, image_point.y,
                                                          object_point.x, object_point.y, object_point.z,
                                                          intrinsics_data, distortion_data));

                    problem.AddResidualBlock(cost_function, nullptr, extrinsics_ptr);
                }
            }
        }

        // 设置参数块的固定状态
        // 只有当同时优化内参和/或畸变时，这些参数块才会被添加到问题中
        if (optimize_intrinsics || optimize_distortion) {
            if (!optimize_intrinsics) {
                problem.SetParameterBlockConstant(intrinsics_data);
            }
            if (!optimize_distortion) {
                problem.SetParameterBlockConstant(distortion_data);
            }
        }

        // 应用约束条件
        if (params.fix_principal_point && optimize_intrinsics) {
            // 固定主点
            std::vector<int> constant_intrinsics = {2, 3}; // cx, cy
            ceres::SubsetParameterization* subset_parameterization =
                new ceres::SubsetParameterization(4, constant_intrinsics);
            problem.SetParameterization(intrinsics_data, subset_parameterization);
        }

        if (params.fix_aspect_ratio && optimize_intrinsics) {
            // 这里可以添加纵横比约束的实现
            // 需要自定义参数化类
        }

        if (params.zero_tangent_distortion && optimize_distortion) {
            // 固定切向畸变为0
            std::vector<int> constant_distortion = {2, 3}; // p1, p2
            ceres::SubsetParameterization* subset_parameterization =
                new ceres::SubsetParameterization(5, constant_distortion);
            problem.SetParameterization(distortion_data, subset_parameterization);
        }

        // 更新求解器选项
        solver_options_.max_num_iterations = params.ba_max_iterations;
        solver_options_.function_tolerance = params.ba_function_tolerance;
        solver_options_.parameter_tolerance = params.ba_parameter_tolerance;
        solver_options_.gradient_tolerance = params.ba_gradient_tolerance;

        // 求解
        LOG_INFO_FUNC("CeresBundleAdjuster", "开始Ceres Bundle Adjustment优化...");
        ceres::Solve(solver_options_, &problem, &summary_);

        // 检查求解结果
        if (summary_.termination_type == ceres::CONVERGENCE ||
            summary_.termination_type == ceres::USER_SUCCESS) {
            
            // 更新优化后的参数
            updateExtrinsicsFromParameters(extrinsics_data, image_data);
            
            if (optimize_intrinsics) {
                updateIntrinsicsFromParameters(intrinsics_data, camera_intrinsics);
            }
            
            if (optimize_distortion) {
                updateDistortionFromParameters(distortion_data, distortion_coeffs);
            }

            LOG_INFO_FUNC("CeresBundleAdjuster",
                std::string("Bundle Adjustment优化成功完成\n") +
                "初始代价: " + std::to_string(summary_.initial_cost) + "\n" +
                "最终代价: " + std::to_string(summary_.final_cost) + "\n" +
                "迭代次数: " + std::to_string(summary_.num_successful_steps) + "\n" +
                "求解时间: " + std::to_string(summary_.total_time_in_seconds) + "秒");
            
            return true;
        } else {
            LOG_ERROR_FUNC("CeresBundleAdjuster", 
                "Bundle Adjustment优化失败: " + summary_.BriefReport());
            return false;
        }

    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("CeresBundleAdjuster", "Bundle Adjustment异常: " + std::string(e.what()));
        return false;
    }
}

void CeresBundleAdjuster::parameterizeExtrinsics(const std::vector<core::CalibrationImageData>& image_data,
                                                 std::vector<double>& extrinsics_data) {
    size_t valid_idx = 0;
    for (const auto& data : image_data) {
        if (data.corners_found && !data.image_points.empty()) {
            // 旋转向量 (Rodrigues表示)
            extrinsics_data[valid_idx * 6 + 0] = data.extrinsics.rotation_vector.at<double>(0);
            extrinsics_data[valid_idx * 6 + 1] = data.extrinsics.rotation_vector.at<double>(1);
            extrinsics_data[valid_idx * 6 + 2] = data.extrinsics.rotation_vector.at<double>(2);
            
            // 平移向量
            extrinsics_data[valid_idx * 6 + 3] = data.extrinsics.translation_vector.at<double>(0);
            extrinsics_data[valid_idx * 6 + 4] = data.extrinsics.translation_vector.at<double>(1);
            extrinsics_data[valid_idx * 6 + 5] = data.extrinsics.translation_vector.at<double>(2);
            
            valid_idx++;
        }
    }
}

void CeresBundleAdjuster::updateExtrinsicsFromParameters(const std::vector<double>& extrinsics_data,
                                                        std::vector<core::CalibrationImageData>& image_data) {
    size_t valid_idx = 0;
    for (auto& data : image_data) {
        if (data.corners_found && !data.image_points.empty()) {
            // 更新旋转向量
            data.extrinsics.rotation_vector.at<double>(0) = extrinsics_data[valid_idx * 6 + 0];
            data.extrinsics.rotation_vector.at<double>(1) = extrinsics_data[valid_idx * 6 + 1];
            data.extrinsics.rotation_vector.at<double>(2) = extrinsics_data[valid_idx * 6 + 2];
            
            // 更新平移向量
            data.extrinsics.translation_vector.at<double>(0) = extrinsics_data[valid_idx * 6 + 3];
            data.extrinsics.translation_vector.at<double>(1) = extrinsics_data[valid_idx * 6 + 4];
            data.extrinsics.translation_vector.at<double>(2) = extrinsics_data[valid_idx * 6 + 5];
            
            // 更新旋转矩阵
            cv::Rodrigues(data.extrinsics.rotation_vector, data.extrinsics.rotation_matrix);
            
            valid_idx++;
        }
    }
}

void CeresBundleAdjuster::parameterizeIntrinsics(const core::CameraIntrinsics& intrinsics, double* intrinsics_data) {
    intrinsics_data[0] = intrinsics.fx;
    intrinsics_data[1] = intrinsics.fy;
    intrinsics_data[2] = intrinsics.cx;
    intrinsics_data[3] = intrinsics.cy;
}

void CeresBundleAdjuster::updateIntrinsicsFromParameters(const double* intrinsics_data, core::CameraIntrinsics& intrinsics) {
    intrinsics.fx = intrinsics_data[0];
    intrinsics.fy = intrinsics_data[1];
    intrinsics.cx = intrinsics_data[2];
    intrinsics.cy = intrinsics_data[3];
}

void CeresBundleAdjuster::parameterizeDistortion(const core::DistortionCoefficients& distortion, double* distortion_data) {
    distortion_data[0] = distortion.k1;
    distortion_data[1] = distortion.k2;
    distortion_data[2] = distortion.p1;
    distortion_data[3] = distortion.p2;
    distortion_data[4] = distortion.k3;
}

void CeresBundleAdjuster::updateDistortionFromParameters(const double* distortion_data, core::DistortionCoefficients& distortion) {
    distortion.k1 = distortion_data[0];
    distortion.k2 = distortion_data[1];
    distortion.p1 = distortion_data[2];
    distortion.p2 = distortion_data[3];
    distortion.k3 = distortion_data[4];
}

#endif // USE_CERES_SOLVER

} // namespace calibration
} // namespace camera_calibration
