#include "core/calibration_manager.h"
#include "calibration/forward_calibrator.h"
#include "calibration/extrinsic_calibrator.h"
#include "processing/image_enhancer.h"
#include "processing/feature_detector.h"
#include "processing/coordinate_mapper.h"
#include "utils/file_utils.h"
#include "utils/common_utils.h"
#include "core/constants.h"
#include <iostream>
#include <fstream>
#include <chrono>
#include <filesystem>

namespace camera_calibration {
namespace core {

CalibrationManager::CalibrationManager(ConfigManager& config_manager)
    : config_manager_(config_manager)
    , initialized_(false)
    , debug_mode_(false)
    , COLS_(0)  // 将在initialize()中从配置文件计算
    , ROWS_(0)  // 将在initialize()中从配置文件计算
    , DIS_TO_CAMERA_(1.0)
    , DIS_TO_CAMERA2CENTER_(0.0) {

    // 初始化与原项目完全一致的世界坐标轴
    h_axis_ = {0};
    w_axis_ = {0};

    // 网格数据结构将在initialize()中初始化
}

CalibrationManager::~CalibrationManager() {
    cleanup();
}

bool CalibrationManager::initialize() {
    try {
        // 获取配置参数
        camera_intrinsics_ = config_manager_.getCameraIntrinsics();
        distortion_coeffs_ = config_manager_.getDistortionCoefficients();
        processing_params_ = config_manager_.getImageProcessingParams();
        blob_params_ = config_manager_.getBlobDetectorParams();

        // 从配置文件计算COLS_和ROWS_
        if (!calculateGridDimensions()) {
            LOG_ERROR_FUNC("CalibrationManager", "计算网格维度失败");
            return false;
        }

        // 从配置文件加载世界坐标轴（与原项目完全一致）
        h_axis_ = config_manager_.getWorldHAxis();
        w_axis_ = config_manager_.getWorldWAxis();
        DIS_TO_CAMERA_ = config_manager_.getDistanceToCamera();
        DIS_TO_CAMERA2CENTER_ = config_manager_.getDistanceToCameraCenter();

        LOG_INFO_FUNC("CalibrationManager", "世界坐标轴加载完成:");
        LOG_INFO_FUNC("CalibrationManager", "  h_axis 长度: " + std::to_string(h_axis_.size()));
        LOG_INFO_FUNC("CalibrationManager", "  w_axis 长度: " + std::to_string(w_axis_.size()));
        LOG_INFO_FUNC("CalibrationManager", "  DIS_TO_CAMERA: " + std::to_string(DIS_TO_CAMERA_));
        LOG_INFO_FUNC("CalibrationManager", "  DIS_TO_CAMERA2CENTER: " + std::to_string(DIS_TO_CAMERA2CENTER_));

        // 初始化网格数据结构
        if (!initializeGridDataStructures()) {
            LOG_ERROR_FUNC("CalibrationManager", "初始化网格数据结构失败");
            return false;
        }

        // 初始化各个模块
        if (!initializeModules()) {
            LOG_ERROR_FUNC("CalibrationManager", "模块初始化失败");
            return false;
        }

        initialized_ = true;
        return true;

    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("CalibrationManager", "初始化异常: " + std::string(e.what()));
        return false;
    }
}

ErrorCode CalibrationManager::performCalibration(const std::string& input_image_path, const std::string& output_path, const std::string& debug_path) {
    if (!initialized_) {
        LOG_ERROR_FUNC("CalibrationManager", "标定管理器未初始化");
        return ErrorCode::INVALID_PARAMETERS;
    }
    
    try {
        // 验证输入参数
        if (!validateInputs(input_image_path, output_path)) {
            return ErrorCode::INVALID_PARAMETERS;
        }
        
        // 创建输出目录
        if (!createOutputDirectory(output_path)) {
            return ErrorCode::FILE_NOT_FOUND;
        }

        if(debug_mode_){
            if (!createOutputDirectory(debug_path)) {
            return ErrorCode::FILE_NOT_FOUND;
        }
        }
        
            
        // 1. 执行前向标定
        cv::Mat calibrated_image;
        ErrorCode forward_result = performForwardCalibration(input_image_path, output_path, debug_path, calibrated_image);
        if (forward_result != ErrorCode::SUCCESS) {
            LOG_ERROR_FUNC("CalibrationManager", "前向标定失败");
            return forward_result;
        }
        
        LOG_INFO_FUNC("CalibrationManager", "前向标定完成");
        
        // 2. 执行特征检测和映射
        ErrorCode feature_result = performFeatureDetectionAndMapping(calibrated_image, output_path, debug_path);
        if (feature_result != ErrorCode::SUCCESS) {
            LOG_ERROR_FUNC("CalibrationManager", "特征检测和映射失败");
            return feature_result;
        }
        
        last_stats_.status_message = "标定成功完成";
        
        return ErrorCode::SUCCESS;
        
    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("CalibrationManager", "标定过程异常: " + std::string(e.what()));
        last_stats_.corners_detected = false;
        last_stats_.error_message = e.what();
        return ErrorCode::CALIBRATION_FAILED;
    }
}

ErrorCode CalibrationManager::performForwardCalibration(const std::string& input_image_path,
                                                       const std::string& output_path, const std::string& debug_path,
                                                       cv::Mat& calibrated_image) {
    try {
        if (!forward_calibrator_) {
            LOG_ERROR_FUNC("CalibrationManager", "前向标定器未初始化");
            return ErrorCode::INVALID_PARAMETERS;
        }

        // 调用前向标定器执行标定
        ErrorCode result = forward_calibrator_->performCalibration(input_image_path, output_path, debug_path, calibrated_image);



        return result;

    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("CalibrationManager", "前向标定异常: " + std::string(e.what()));
        return ErrorCode::FORWARD_CALIB_FAILED;
    }
}

ErrorCode CalibrationManager::performFeatureDetectionAndMapping(const cv::Mat& calibrated_image,
                                                               const std::string& output_path, const std::string& debug_path) {
    try {
        if (!image_enhancer_ || !feature_detector_ || !coordinate_mapper_) {
            LOG_ERROR_FUNC("CalibrationManager", "处理模块未初始化");
            return ErrorCode::INVALID_PARAMETERS;
        }

        LOG_INFO_FUNC("CalibrationManager", "开始图像增强处理...");

        // 1. 图像增强 (使用与原项目一致的MSRCR算法)
        cv::Mat enhanced_image;
        if (!image_enhancer_->enhanceImage(calibrated_image, enhanced_image)) {
            LOG_ERROR_FUNC("CalibrationManager", "图像增强失败");
            enhanced_image = calibrated_image.clone();
        }

        // 保存增强后的图像 (与saveDebugImage保存方式统一)
        saveEnhancedImage(enhanced_image, constants::standard_filenames::ENHANCED_IMAGE, debug_path);

        LOG_INFO_FUNC("CalibrationManager", "开始特征点检测...");

        // 2. 特征点检测
        std::vector<core::FeaturePoint> feature_points;
        if (!feature_detector_->detectFeatures(enhanced_image, feature_points, debug_path)) {
            LOG_ERROR_FUNC("CalibrationManager", "特征点检测失败");
            return ErrorCode::FEATURE_DETECTION_FAILED;
        }
        LOG_INFO_FUNC("CalibrationManager", "开始坐标映射...");

        // 3. 坐标映射和结果保存
        std::string csv_file_path = output_path + constants::standard_filenames::OUTPUT_CSV;
        if (!coordinate_mapper_->saveCalibrationResults(feature_points, csv_file_path)) {
            LOG_ERROR_FUNC("CalibrationManager", "保存标定结果失败");
            return ErrorCode::FEATURE_DETECTION_FAILED;
        }

        LOG_INFO_FUNC("CalibrationManager", "标定结果已保存到: " + csv_file_path);


        // 5. 生成查找表（与原项目的 _generate_table 对应）
        if (!generateCalibrationTable(csv_file_path, constants::standard_filenames::DISTANCE_TABLE, output_path)) {
            LOG_ERROR_FUNC("CalibrationManager", "生成查找表失败");
            return ErrorCode::FEATURE_DETECTION_FAILED;
        }

        LOG_INFO_FUNC("CalibrationManager", "查找表生成完成");


        return ErrorCode::SUCCESS;

    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("CalibrationManager", "特征检测和映射异常: " + std::string(e.what()));
        return ErrorCode::FEATURE_DETECTION_FAILED;
    }
}

bool CalibrationManager::generateCalibrationTable(const std::string& csv_file,
                                                 const std::string& table_name,
                                                 const std::string& output_path) {
    try {
        LOG_INFO_FUNC("CalibrationManager", "生成标定查找表: " + table_name);

        // 调用与原项目完全一致的 _generate_table 实现
        return generateTableFromCSV(csv_file, table_name, output_path);

    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("CalibrationManager", "生成查找表异常: " + std::string(e.what()));
        return false;
    }
}

bool CalibrationManager::generateTableFromCSV(const std::string& csv_file,
                                             const std::string& table_name,
                                             const std::string& output_path) {
    try {
        // 获取配置参数（与原项目完全一致）
        auto processing_params = config_manager_.getImageProcessingParams();

        // 从配置中获取查找表参数
        int IMGX_RANGE[2] = {processing_params.img_x_range.start,
                            processing_params.img_x_range.end};
        int IMGY_RANGE[2] = {processing_params.img_y_range.start,
                            processing_params.img_y_range.end};

        int table_height = IMGY_RANGE[1] - IMGY_RANGE[0] + 1;
        int table_width = IMGX_RANGE[1] - IMGX_RANGE[0] + 1;

        int X_MIN = static_cast<int>(processing_params.world_x_min);
        int X_MAX = static_cast<int>(processing_params.world_x_max);
        int Y_MIN = static_cast<int>(processing_params.world_y_min);
        int Y_MAX = static_cast<int>(processing_params.world_y_max);

        LOG_INFO_FUNC("CalibrationManager", "查找表参数: table_height=" + std::to_string(table_height) +
                ", table_width=" + std::to_string(table_width));

        // 第一步：分配内存并初始化（使用智能指针管理内存）
        std::unique_ptr<float[]> loc(new float[2 * table_height * table_width]{});

        // 第二步：加载标定数据（与原项目Calibration calib(config); calib.read_from_csv(csvFile);完全一致）
        if (!loadCalibrationDataFromCSV(csv_file)) {
            LOG_ERROR_FUNC("CalibrationManager", "加载标定数据失败");
            return false;
        }

        // 第三步：创建二进制表文件（与原项目完全一致）
        std::string full_path = output_path + "/" + table_name;
        std::ofstream file(full_path, std::ios_base::binary | std::ios_base::trunc);
        if (!file.good()) {
            LOG_ERROR_FUNC("CalibrationManager", "无法创建二进制表文件");
            return false;
        }

        // 第四步：创建日志文件（与原项目完全一致）
        std::ofstream log(output_path + constants::standard_filenames::LOG_FILE);
        if (!log.is_open()) {
            LOG_ERROR_FUNC("CalibrationManager", "无法创建日志文件");
            file.close();
            return false;
        }
        log << " Point# " << " x " << " y " << " x_loc " << " y_loc " << std::endl;



        // 第五步：遍历所有像素点进行坐标映射（与原项目完全一致）
        int k = 0;
        for (int y = IMGY_RANGE[0]; y <= IMGY_RANGE[1]; y++) {
            for (int x = IMGX_RANGE[0]; x <= IMGX_RANGE[1]; x++) {
                core::Point2D pixel_point(x, y);
                core::CalibrationResult result = mapPixelToWorldOriginal(pixel_point);

                // 处理无效映射结果（与原项目完全一致）
                if (result.world_position.x == -1 && result.world_position.y == -1) {
                    result.world_position.x = 0;
                    result.world_position.y = 0;
                }

                loc[k++] = static_cast<float>(result.world_position.x);
                loc[k++] = static_cast<float>(result.world_position.y);
            }
        }

        return generateTableWithInterpolationAndOutput(loc.get(), table_height, table_width,
                                                      IMGX_RANGE, IMGY_RANGE,
                                                      X_MIN, X_MAX, Y_MIN, Y_MAX,
                                                      file, log, output_path);

    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("CalibrationManager", "生成查找表异常: " + std::string(e.what()));
        return false;
    }
}

bool CalibrationManager::generateTableWithInterpolationAndOutput(float* loc, int table_height, int table_width,
                                                                int IMGX_RANGE[2], int IMGY_RANGE[2],
                                                                int X_MIN, int X_MAX, int Y_MIN, int Y_MAX,
                                                                std::ofstream& file, std::ofstream& log, const std::string& output_path) {
    try {
        // 第一步：寻找有效数据的边界
        TableBounds bounds;
        if (!findValidDataBounds(loc, table_width, IMGX_RANGE, IMGY_RANGE, bounds)) {
            LOG_ERROR_FUNC("CalibrationManager", "未找到有效数据边界");
            return false;
        }

        // 第二步：逐行插值处理
        if (!performRowwiseInterpolation(loc, table_width, IMGX_RANGE, IMGY_RANGE)) {
            LOG_ERROR_FUNC("CalibrationManager", "行插值处理失败");
            return false;
        }

        // 第三步：最终化查找表
        return finalizeLookupTable(loc, table_height, table_width, X_MIN, X_MAX, Y_MIN, Y_MAX, file, log, output_path);

    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("CalibrationManager", "查找表插值处理异常: " + std::string(e.what()));
        return false;
    }
}



// 与原项目完全一致的辅助函数实现
int CalibrationManager::findNonZeroLeft(float* loc_temp, int pixel_row, int table_width,
                                       int IMGX_RANGE[2], int IMGY_RANGE[2]) {
    int noZeroCol1 = -1;
    for (int i = IMGX_RANGE[1]/2; i >= IMGX_RANGE[0]; i--) {
        float temp = loc_temp[2 * ((pixel_row - IMGY_RANGE[0]) * table_width + (i - IMGX_RANGE[0]))];
        if (temp == 0) {
            noZeroCol1 = i + 1;
            break;
        }
    }
    return noZeroCol1;
}

int CalibrationManager::findNonZeroRight(float* loc_temp, int pixel_row, int table_width,
                                        int IMGX_RANGE[2], int IMGY_RANGE[2]) {
    int noZeroCol2 = -1;
    for (int i = IMGX_RANGE[1]/2; i <= IMGX_RANGE[1]; i++) {
        float temp = loc_temp[2 * ((pixel_row - IMGY_RANGE[0]) * table_width + (i - IMGX_RANGE[0]))];
        if (temp == 0) {
            noZeroCol2 = i - 1;
            break;
        }
    }
    return noZeroCol2;
}

bool CalibrationManager::checkColumnConnectivity(float* loc_temp, int row, int col1, int col2,
                                                int table_width, int IMGX_RANGE[2], int IMGY_RANGE[2]) {
    bool colConnect = false;
    for (int j = col1; j <= col2; ++j) {
        float temp = loc_temp[2 * ((row - IMGY_RANGE[0]) * table_width + (j - IMGX_RANGE[0]))];
        if (temp == 0) {
            colConnect = false;
            return colConnect;
        } else {
            colConnect = true;
        }
    }
    return colConnect;
}

int CalibrationManager::calcLoc(double loc, int s_min, int s_max) {
    int loc_temp;
    int int_loc = static_cast<int>((loc - s_min) / (s_max - s_min) * 255 + 0.5);
    if (int_loc < 0) {
        loc_temp = 0;
    } else if (int_loc > 255) {
        loc_temp = 255;
    } else {
        loc_temp = int_loc;
    }
    return loc_temp;
}

bool CalibrationManager::finalizeLookupTable(float* loc, int table_height, int table_width,
                                            int X_MIN, int X_MAX, int Y_MIN, int Y_MAX,
                                            std::ofstream& file, std::ofstream& log, const std::string& output_path) {
    try {
        // 第一步：输出调试信息到文本文件（与原项目完全一致）
        std::ofstream debug_file(output_path + constants::standard_filenames::LOC_FILE);
        for (int i = 0; i < table_height * 2 * table_width; ++i) {
            debug_file << loc[i] << " ";
        }
        debug_file.close();

        // 第二步：量化并写入二进制文件（使用智能指针管理内存）
        std::unique_ptr<unsigned char[]> loc_xy(new unsigned char[2 * table_width * table_height]);
        for (int i = 0; i < 2 * table_height * table_width; i += 2) {
            int nx = calcLoc(loc[i], X_MIN, X_MAX);
            int ny = calcLoc(loc[i + 1], Y_MIN, Y_MAX);
            loc_xy[i] = static_cast<uint8_t>(nx);
            loc_xy[i + 1] = static_cast<uint8_t>(ny);
            file << loc_xy[i] << loc_xy[i + 1];
        }

        file.close();
        log.close();

        LOG_INFO_FUNC("CalibrationManager", ">>>Done!");
        LOG_INFO_FUNC("CalibrationManager", ">>>Number of Points Processed: " + std::to_string(2 * table_height * table_width));

        return true;

    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("CalibrationManager", "查找表最终处理异常: " + std::string(e.what()));
        return false;
    }
}

// 与原项目Calibration类完全一致的方法实现
bool CalibrationManager::loadCalibrationDataFromCSV(const std::string& csv_file) {
    try {
        // 读文件（与原项目read_from_csv完全一致）
        std::ifstream csvFile(csv_file, std::ios::in);
        if (!csvFile.is_open()) {
            LOG_ERROR_FUNC("CalibrationManager", "无法打开CSV文件: " + csv_file);
            return false;
        }

        std::string lineStr;
        std::string str;
        int intValue;
        std::vector<std::vector<int>> int2DArray;

        while (std::getline(csvFile, lineStr)) {
            // 存成二维表结构
            std::stringstream ss(lineStr);
            std::vector<int> int1DArray;
            // 按照逗号分隔
            while (std::getline(ss, str, ',')) {
                std::stringstream ssm(str);
                ssm >> intValue;
                int1DArray.push_back(intValue);
            }
            int2DArray.push_back(int1DArray);
        }
        csvFile.close();

        // 处理数据（与原项目完全一致）
        PointInfo pix, pos;
        for (auto it = int2DArray.begin(); it != int2DArray.end(); it++) {
            int id_x = (*it)[0];
            int id_y = (*it)[1];

            double pixel_x = (*it)[2];
            double pixel_y = (*it)[3];

            // index of row: -7 -6 -5 -4 -3 -2 -1 1 2 3 4 5 6 7
            if (id_x < 0)
                id_x += 7;
            else
                id_x += 6;
            id_y -= 1; // each col starts from 1 in csv;

            pix.x = pixel_x;
            pix.y = pixel_y;

            if (id_y >= 0 && id_y < ROWS_ && id_x >= 0 && id_x < COLS_) {
                pixels3D_[id_y][id_x] = pix;
                pos.x = h_axis_[id_y] + DIS_TO_CAMERA_;
                pos.y = w_axis_[id_x] + DIS_TO_CAMERA2CENTER_;
                poses3D_[id_y][id_x] = pos;
            }
        }

        LOG_INFO_FUNC("CalibrationManager", "CSV数据加载完成，共处理 " + std::to_string(int2DArray.size()) + " 条记录");
        return true;

    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("CalibrationManager", "加载CSV数据异常: " + std::string(e.what()));
        return false;
    }
}

core::CalibrationResult CalibrationManager::mapPixelToWorldOriginal(const core::Point2D& pixel_point) {
    // 与原项目map_pixel_to_location完全一致的实现
    int rows = ROWS_ - 1;
    int cols = COLS_ - 1;
    core::CalibrationResult ret;
    ret.world_position.x = -1;
    ret.world_position.y = -1;
    ret.pixel_position = pixel_point;
    ret.confidence = 0.0;

    PointInfo selectedPoint(pixel_point.x, pixel_point.y);

    for(int i = 0; i < rows; i++){
        for(int j = 0; j < cols; j++){
            // 跳过特殊单元（与原项目完全一致）
            if (!((i==1&&j==12)||(i==2&&j==11)||(i==3&&j==9)||(i==4&&j==8))) {

                PointInfo left_top = pixels3D_[i][j];
                PointInfo left_top_pose = poses3D_[i][j];
                if(left_top.x == 0 || left_top.y == 0)
                    continue;

                PointInfo right_bottom = pixels3D_[i+1][j+1];
                PointInfo right_bottom_pose = poses3D_[i+1][j+1];
                if(right_bottom.x == 0 || right_bottom.y == 0)
                    continue;

                PointInfo left_bottom = pixels3D_[i+1][j];
                PointInfo left_bottom_pose = poses3D_[i+1][j];

                PointInfo right_top = pixels3D_[i][j+1];
                PointInfo right_top_pose = poses3D_[i][j+1];

                // 第一个三角形检查
                if (!(left_bottom.x == 0 || left_bottom.y == 0)) {
                    double va = crossProduct(left_top, left_bottom, pixel_point);
                    double vb = crossProduct(left_bottom, right_bottom, pixel_point);
                    double vc = crossProduct(right_bottom, left_top, pixel_point);

                    if (va<=0&&vb<=0&&vc<=0) {
                        // 开始插值
                        double area1 = calculateArea(left_top, left_bottom, pixel_point);
                        double area2 = calculateArea(left_bottom, right_bottom, pixel_point);
                        double area3 = calculateArea(right_bottom, left_top, pixel_point);
                        double x = right_bottom_pose.x*area1/(area1+area2+area3) +
                                  left_top_pose.x*area2/(area1+area2+area3) +
                                  left_bottom_pose.x*area3/(area1+area2+area3);
                        double y = right_bottom_pose.y*area1/(area1+area2+area3) +
                                  left_top_pose.y*area2/(area1+area2+area3) +
                                  left_bottom_pose.y*area3/(area1+area2+area3);
                        ret.world_position.x = x;
                        ret.world_position.y = y;
                        ret.confidence = 1.0;
                        return ret;
                    }
                }

                // 第二个三角形检查
                if (!(right_top.x == 0 || right_top.y == 0)) {
                    double va = crossProduct(left_top, right_top, pixel_point);
                    double vb = crossProduct(right_top, right_bottom, pixel_point);
                    double vc = crossProduct(right_bottom, left_top, pixel_point);

                    if(va>=0&&vb>=0&&vc>=0) {
                        // 开始插值
                        double area1 = calculateArea(left_top, right_top, pixel_point);
                        double area2 = calculateArea(right_top, right_bottom, pixel_point);
                        double area3 = calculateArea(right_bottom, left_top, pixel_point);
                        double x = right_bottom_pose.x*area1/(area1+area2+area3) +
                                  left_top_pose.x*area2/(area1+area2+area3) +
                                  right_top_pose.x*area3/(area1+area2+area3);
                        double y = right_bottom_pose.y*area1/(area1+area2+area3) +
                                  left_top_pose.y*area2/(area1+area2+area3) +
                                  right_top_pose.y*area3/(area1+area2+area3);
                        ret.world_position.x = x;
                        ret.world_position.y = y;
                        ret.confidence = 1.0;
                        return ret;
                    }
                }
            }
            else {
                // 处理特殊单元（与原项目完全一致）
                PointInfo left_top = pixels3D_[i][j];
                PointInfo left_top_pose = poses3D_[i][j];
                PointInfo right_top = pixels3D_[i][j+1];
                PointInfo right_top_pose = poses3D_[i][j+1];
                PointInfo left_bottom = pixels3D_[i+1][j];
                PointInfo left_bottom_pose = poses3D_[i+1][j];

                double va = crossProduct(left_top, right_top, pixel_point);
                if (va < 0) continue;
                double vb = crossProduct(right_top, left_bottom, pixel_point);
                if (vb < 0) continue;
                double vc = crossProduct(left_bottom, left_top, pixel_point);
                if (vc < 0) continue;

                // 开始插值
                double area1 = calculateArea(left_top, left_bottom, pixel_point);
                double area2 = calculateArea(left_bottom, right_top, pixel_point);
                double area3 = calculateArea(right_top, left_top, pixel_point);
                double x = right_top_pose.x*area1/(area1+area2+area3) +
                          left_top_pose.x*area2/(area1+area2+area3) +
                          left_bottom_pose.x*area3/(area1+area2+area3);
                double y = right_top_pose.y*area1/(area1+area2+area3) +
                          left_top_pose.y*area2/(area1+area2+area3) +
                          left_bottom_pose.y*area3/(area1+area2+area3);
                ret.world_position.x = x;
                ret.world_position.y = y;
                ret.confidence = 1.0;
                return ret;
            }
        }
    }

    return ret;
}

double CalibrationManager::crossProduct(const PointInfo& p1, const PointInfo& p2, const core::Point2D& p3) {
    // 与原项目cross_product完全一致
    PointInfo dpa, dpb;
    dpa.x = p2.x - p1.x;
    dpa.y = p2.y - p1.y;

    dpb.x = p3.x - p1.x;
    dpb.y = p3.y - p1.y;

    return (dpa.x * dpb.y - dpa.y * dpb.x);
}

double CalibrationManager::calculateArea(const PointInfo& p1, const PointInfo& p2, const core::Point2D& p3) {
    // 与原项目cal_area完全一致
    double area = 0.5 * std::abs(p1.x*p2.y + p2.x*p3.y + p3.x*p1.y - p1.x*p3.y - p2.x*p1.y - p3.x*p2.y);
    return area;
}

// 私有方法实现
bool CalibrationManager::initializeModules() {
    try {
        // 初始化前向标定器
        forward_calibrator_ = std::make_unique<calibration::ForwardCalibrator>();
        if (!forward_calibrator_->initialize(camera_intrinsics_, distortion_coeffs_, processing_params_)) {
            LOG_ERROR_FUNC("CalibrationManager", "前向标定器初始化失败");
            return false;
        }
        forward_calibrator_->setDebugMode(debug_mode_);

        // 初始化外参标定器
        extrinsic_calibrator_ = std::make_unique<calibration::ExtrinsicCalibrator>();
        auto extrinsic_params = config_manager_.getExtrinsicCalibrationParams();
        if (!extrinsic_calibrator_->initialize(camera_intrinsics_, distortion_coeffs_, extrinsic_params)) {
            LOG_ERROR_FUNC("CalibrationManager", "外参标定器初始化失败");
            return false;
        }
        extrinsic_calibrator_->setDebugMode(debug_mode_);

        // 初始化图像增强器
        image_enhancer_ = std::make_unique<processing::ImageEnhancer>();
        if (!image_enhancer_->initialize(processing_params_)) {
            LOG_ERROR_FUNC("CalibrationManager", "图像增强器初始化失败");
            return false;
        }
        image_enhancer_->setDebugMode(debug_mode_);

        // 初始化特征检测器
        feature_detector_ = std::make_unique<processing::FeatureDetector>();
        auto left_counts = config_manager_.getLeftColumnPointCounts();
        auto right_counts = config_manager_.getRightColumnPointCounts();
        if (!feature_detector_->initialize(blob_params_, processing_params_, left_counts, right_counts)) {
            LOG_ERROR_FUNC("CalibrationManager", "特征检测器初始化失败");
            return false;
        }
        feature_detector_->setDebugMode(debug_mode_);

        // 初始化坐标映射器
        coordinate_mapper_ = std::make_unique<processing::CoordinateMapper>();
        if (!coordinate_mapper_->initialize(processing_params_)) {
            LOG_ERROR_FUNC("CalibrationManager", "坐标映射器初始化失败");
            return false;
        }
        coordinate_mapper_->setDebugMode(debug_mode_);

        // LOG_INFO_FUNC("CalibrationManager", "所有模块初始化完成");
        return true;

    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("CalibrationManager", "模块初始化异常: " + std::string(e.what()));
        return false;
    }
}

void CalibrationManager::cleanup() {
    // 清理资源
    initialized_ = false;
}

bool CalibrationManager::validateInputs(const std::string& input_path, const std::string& output_path) {
    if (input_path.empty()) {
        LOG_ERROR_FUNC("CalibrationManager", "输入路径为空");
        return false;
    }
    
    if (!utils::FileUtils::exists(input_path)) {
        LOG_ERROR_FUNC("CalibrationManager", "输入文件不存在: " + input_path);
        return false;
    }
    
    if (output_path.empty()) {
        LOG_ERROR_FUNC("CalibrationManager", "输出路径为空");
        return false;
    }
    
    return true;
}

bool CalibrationManager::createOutputDirectory(const std::string& output_path) {
    try {
        if (!utils::FileUtils::exists(output_path)) {
            if (!utils::FileUtils::createDirectories(output_path)) {
                LOG_INFO_FUNC("CalibrationManager", "创建输出目录: " + output_path);
            }
        }
        return true;
        
    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("CalibrationManager", "创建输出目录异常: " + std::string(e.what()));
        return false;
    }
}





// 重构后的子函数实现

bool CalibrationManager::findValidDataBounds(float* loc, int table_width, int IMGX_RANGE[2], int IMGY_RANGE[2], TableBounds& bounds) {
    try {
        // 寻找第一个非零点
        bool found_first = false;
        for (int i = IMGY_RANGE[0]; i <= IMGY_RANGE[1] && !found_first; ++i) {
            for (int j = IMGX_RANGE[0]; j <= IMGX_RANGE[1]; ++j) {
                if (loc[2 * ((i - IMGY_RANGE[0]) * table_width + (j - IMGX_RANGE[0]))] != 0) {
                    bounds.minrow = i;
                    bounds.mincol = j;
                    found_first = true;
                    break;
                }
            }
        }

        // 寻找最后一个非零点
        bool found_last = false;
        for (int i = IMGY_RANGE[1]; i >= IMGY_RANGE[0] && !found_last; i--) {
            for (int j = IMGX_RANGE[1]; j >= IMGX_RANGE[0]; j--) {
                if (loc[2 * ((i - IMGY_RANGE[0]) * table_width + (j - IMGX_RANGE[0]))] != 0) {
                    bounds.maxrow = i;
                    bounds.maxcol = j;
                    found_last = true;
                    break;
                }
            }
        }

        if (!found_first || !found_last) {
            LOG_ERROR_FUNC("CalibrationManager", "未找到有效数据边界");
            return false;
        }


        return true;

    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("CalibrationManager", "查找数据边界异常: " + std::string(e.what()));
        return false;
    }
}

bool CalibrationManager::performRowwiseInterpolation(float* loc, int table_width, int IMGX_RANGE[2], int IMGY_RANGE[2]) {
    try {
        using namespace camera_calibration::constants;

        for (int i = IMGY_RANGE[0]; i <= IMGY_RANGE[1]; i++) {
            int col1 = findNonZeroLeft(loc, i, table_width, IMGX_RANGE, IMGY_RANGE);
            int col2 = findNonZeroRight(loc, i, table_width, IMGX_RANGE, IMGY_RANGE);

            if (col2 - col1 > COLUMN_WIDTH_THRESHOLD) {
                bool colConnect = checkColumnConnectivity(loc, i, col1, col2, table_width, IMGX_RANGE, IMGY_RANGE);

                if (colConnect) {
                    interpolateLeftHalf(loc, i, col1, col2, table_width, IMGX_RANGE, IMGY_RANGE);
                    interpolateRightHalf(loc, i, col1, col2, table_width, IMGX_RANGE, IMGY_RANGE);
                }
            }
        }

        LOG_INFO_FUNC("CalibrationManager", "逐行插值处理完成");
        return true;

    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("CalibrationManager", "行插值处理异常: " + std::string(e.what()));
        return false;
    }
}

void CalibrationManager::interpolateLeftHalf(float* loc, int row, int col1, int col2, int table_width,
                                           int IMGX_RANGE[2], int IMGY_RANGE[2]) {
    // 左半部分插值
    for (int j = IMGX_RANGE[0]; j < IMGX_RANGE[1]/2; j++) {
        if (loc[2 * ((row - IMGY_RANGE[0]) * table_width + (j - IMGX_RANGE[0]))] == 0) {
            loc[2 * ((row - IMGY_RANGE[0]) * table_width + j - IMGX_RANGE[0]) + 1] =
                (loc[2 * ((row - IMGY_RANGE[0]) * table_width + (col1 - IMGX_RANGE[0])) + 1] -
                 loc[2 * ((row - IMGY_RANGE[0]) * table_width + (col2 - IMGX_RANGE[0])) + 1]) /
                (col2 - col1) * (col2 - j) +
                loc[2 * ((row - IMGY_RANGE[0]) * table_width + (col2 - IMGX_RANGE[0])) + 1];
            loc[2 * ((row - IMGY_RANGE[0]) * table_width + j - IMGX_RANGE[0])] =
                loc[2 * ((row - IMGY_RANGE[0]) * table_width + (col1 - IMGX_RANGE[0]))];
        }
    }
}

void CalibrationManager::interpolateRightHalf(float* loc, int row, int col1, int col2, int table_width,
                                            int IMGX_RANGE[2], int IMGY_RANGE[2]) {
    // 右半部分插值
    for (int j = IMGX_RANGE[1]/2; j <= IMGX_RANGE[1]; j++) {
        if (loc[2 * ((row - IMGY_RANGE[0]) * table_width + (j - IMGX_RANGE[0]))] == 0) {
            loc[2 * ((row - IMGY_RANGE[0]) * table_width + j - IMGX_RANGE[0]) + 1] =
                loc[2 * ((row - IMGY_RANGE[0]) * table_width + (col1 - IMGX_RANGE[0])) + 1] -
                ((loc[2 * ((row - IMGY_RANGE[0]) * table_width + (col1 - IMGX_RANGE[0])) + 1] -
                  loc[2 * ((row - IMGY_RANGE[0]) * table_width + (col2 - IMGX_RANGE[0])) + 1]) /
                 (col2 - col1) * (j - col1));
            loc[2 * ((row - IMGY_RANGE[0]) * table_width + j - IMGX_RANGE[0])] =
                loc[2 * ((row - IMGY_RANGE[0]) * table_width + (col2 - IMGX_RANGE[0]))];
        }
    }
}

bool CalibrationManager::calculateGridDimensions() {
    try {
        // 从配置文件中获取正确的网格维度（与原项目完全一致）
        // 使用配置文件中的grid_info值，而不是column_point_counts
        ROWS_ = config_manager_.getWorldGridRows();    // 直接使用配置中的rows值
        COLS_ = config_manager_.getWorldGridCols();    // 直接使用配置中的cols值

        LOG_INFO_FUNC("CalibrationManager", "从配置文件读取网格维度:");
        LOG_INFO_FUNC("CalibrationManager", "  配置中的 grid_rows: " + std::to_string(config_manager_.getWorldGridRows()));
        LOG_INFO_FUNC("CalibrationManager", "  配置中的 grid_cols: " + std::to_string(config_manager_.getWorldGridCols()));
        LOG_INFO_FUNC("CalibrationManager", "  设置 ROWS_: " + std::to_string(ROWS_));
        LOG_INFO_FUNC("CalibrationManager", "  设置 COLS_: " + std::to_string(COLS_));

        // 验证配置值的合理性
        if (ROWS_ <= 0 || COLS_ <= 0) {
            LOG_ERROR_FUNC("CalibrationManager", "配置文件中的网格维度无效: ROWS_=" +
                          std::to_string(ROWS_) + ", COLS_=" + std::to_string(COLS_));
            return false;
        }

        // 验证与原项目的一致性
        if (ROWS_ != 7 || COLS_ != 14) {
            LOG_INFO_FUNC("CalibrationManager", "网格维度与原项目不一致! 期望: 7x14, 实际: " +
                         std::to_string(ROWS_) + "x" + std::to_string(COLS_));
        }

        return true;

    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("CalibrationManager", "计算网格维度异常: " + std::string(e.what()));
        return false;
    }
}

bool CalibrationManager::initializeGridDataStructures() {
    try {
        // 验证网格维度已计算
        if (ROWS_ <= 0 || COLS_ <= 0) {
            LOG_ERROR_FUNC("CalibrationManager", "网格维度未正确计算: ROWS_=" +
                          std::to_string(ROWS_) + ", COLS_=" + std::to_string(COLS_));
            return false;
        }

        // 初始化网格数据结构（与原项目完全一致）
        pixels3D_.clear();
        poses3D_.clear();

        pixels3D_.resize(ROWS_);
        poses3D_.resize(ROWS_);

        for(int rw = 0; rw < ROWS_; rw++){
            pixels3D_[rw].resize(COLS_);
            poses3D_[rw].resize(COLS_);
            for(int cl = 0; cl < COLS_; cl++){
                pixels3D_[rw][cl] = PointInfo();
                poses3D_[rw][cl] = PointInfo();
            }
        }

        LOG_INFO_FUNC("CalibrationManager", "网格数据结构初始化完成: " +
                     std::to_string(ROWS_) + "x" + std::to_string(COLS_) + " = " +
                     std::to_string(ROWS_ * COLS_) + " 个网格点");

        return true;

    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("CalibrationManager", "初始化网格数据结构异常: " + std::string(e.what()));
        return false;
    }
}

void CalibrationManager::saveEnhancedImage(const cv::Mat& image, const std::string& filename,
                                          const std::string& output_path) {
    if (!debug_mode_) return;

    // 使用统一的图像保存方式
    if (utils::FileOperationHelper::saveImageSafely(image, filename, output_path, "CalibrationManager")) {
        LOG_DEBUG_FUNC("CalibrationManager", "增强图像已保存: " + output_path + filename, debug_mode_);
    } else {
        LOG_ERROR_FUNC("CalibrationManager", "保存增强图像失败: " + output_path + "/" + filename);
    }
}

// 外参标定方法实现
ErrorCode CalibrationManager::performExtrinsicCalibration(const std::string& input_path,
                                                         const std::string& output_path,
                                                         CalibrationMode mode) {
    if (!initialized_) {
        LOG_ERROR_FUNC("CalibrationManager", "标定管理器未初始化");
        return ErrorCode::INVALID_PARAMETERS;
    }

    if (!extrinsic_calibrator_) {
        LOG_ERROR_FUNC("CalibrationManager", "外参标定器未初始化");
        return ErrorCode::INVALID_PARAMETERS;
    }

    try {
        // 创建输出目录
        if (!utils::FileOperationHelper::createOutputDirectory(output_path, "CalibrationManager")) {
            return ErrorCode::FILE_NOT_FOUND;
        }

        LOG_INFO_FUNC("CalibrationManager", "开始外参标定，输入路径: " + input_path);

        ExtrinsicCalibrationResult result;
        ErrorCode error_code;

        // 检查输入路径是文件还是目录
        if (std::filesystem::is_directory(input_path)) {
            // 目录模式 - 多图像标定
            // 从配置文件获取图像模式，如果没有配置则使用默认值
            std::string image_pattern = config_manager_.getExtrinsicImagePattern();
            if (image_pattern.empty()) {
                image_pattern = "*.bmp";  // 默认模式
            }
            error_code = extrinsic_calibrator_->calibrateFromDirectory(input_path, image_pattern, result);
        } else if (std::filesystem::is_regular_file(input_path)) {
            // 单文件模式
            CameraExtrinsics extrinsics;
            error_code = extrinsic_calibrator_->calibrateSingleImage(input_path, extrinsics);

            if (error_code == ErrorCode::SUCCESS) {
                // 创建单图像结果
                result.success = true;
                result.total_images = 1;
                result.num_successful_images = 1;

                CalibrationImageData image_data;
                image_data.image_path = input_path;
                image_data.corners_found = true;
                image_data.extrinsics = extrinsics;
                result.image_data.push_back(image_data);

                result.computeStatistics();
            }
        } else {
            LOG_ERROR_FUNC("CalibrationManager", "输入路径无效: " + input_path);
            return ErrorCode::FILE_NOT_FOUND;
        }

        if (error_code != ErrorCode::SUCCESS) {
            LOG_ERROR_FUNC("CalibrationManager", "外参标定失败: " + errorCodeToString(error_code));
            return error_code;
        }

        // 保存标定结果
        if (!extrinsic_calibrator_->saveCalibrationResult(result, output_path)) {
            LOG_WARN_FUNC("CalibrationManager", "保存标定结果失败");
        }

        // 更新统计信息
        last_stats_.successful_detections = result.num_successful_images;
        last_stats_.processed_images = result.total_images;
        last_stats_.min_reprojection_error = result.min_reprojection_error;
        last_stats_.mean_reprojection_error = result.mean_reprojection_error;
        last_stats_.max_reprojection_error = result.max_reprojection_error;
        last_stats_.detection_rate = static_cast<double>(result.num_successful_images) / result.total_images;

        LOG_INFO_FUNC("CalibrationManager",
            "外参标定完成，成功处理 " + std::to_string(result.num_successful_images) +
            " 张图像，平均重投影误差: " + std::to_string(result.mean_reprojection_error));

        return ErrorCode::SUCCESS;

    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("CalibrationManager", "外参标定异常: " + std::string(e.what()));
        return ErrorCode::EXTRINSIC_CALIB_FAILED;
    }
}

ErrorCode CalibrationManager::performSingleImageExtrinsicCalibration(const std::string& image_path,
                                                                    const std::string& output_path) {
    if (!initialized_ || !extrinsic_calibrator_) {
        LOG_ERROR_FUNC("CalibrationManager", "标定管理器或外参标定器未初始化");
        return ErrorCode::INVALID_PARAMETERS;
    }

    try {
        // 创建输出目录
        if (!utils::FileOperationHelper::createOutputDirectory(output_path, "CalibrationManager")) {
            return ErrorCode::FILE_NOT_FOUND;
        }

        LOG_INFO_FUNC("CalibrationManager", "开始单图像外参标定: " + image_path);

        CameraExtrinsics extrinsics;
        ErrorCode error_code = extrinsic_calibrator_->calibrateSingleImage(image_path, extrinsics);

        if (error_code == ErrorCode::SUCCESS) {
            // 保存结果
            std::string result_file = output_path + "/single_image_extrinsics.txt";
            std::ofstream file(result_file);
            if (file.is_open()) {
                file << "# 单图像外参标定结果\n";
                file << "image_path: " << image_path << "\n";
                file << "rotation_vector: [";
                for (int i = 0; i < 3; ++i) {
                    file << extrinsics.rotation_vector.at<double>(i);
                    if (i < 2) file << ", ";
                }
                file << "]\n";
                file << "translation_vector: [";
                for (int i = 0; i < 3; ++i) {
                    file << extrinsics.translation_vector.at<double>(i);
                    if (i < 2) file << ", ";
                }
                file << "]\n";
                file.close();
            }

            LOG_INFO_FUNC("CalibrationManager", "单图像外参标定完成");
        } else {
            LOG_ERROR_FUNC("CalibrationManager", "单图像外参标定失败: " + errorCodeToString(error_code));
        }

        return error_code;

    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("CalibrationManager", "单图像外参标定异常: " + std::string(e.what()));
        return ErrorCode::EXTRINSIC_CALIB_FAILED;
    }
}

ErrorCode CalibrationManager::performMultiImageExtrinsicCalibration(const std::vector<std::string>& image_paths,
                                                                   const std::string& output_path) {
    if (!initialized_ || !extrinsic_calibrator_) {
        LOG_ERROR_FUNC("CalibrationManager", "标定管理器或外参标定器未初始化");
        return ErrorCode::INVALID_PARAMETERS;
    }

    try {
        // 创建输出目录
        if (!utils::FileOperationHelper::createOutputDirectory(output_path, "CalibrationManager")) {
            return ErrorCode::FILE_NOT_FOUND;
        }

        LOG_INFO_FUNC("CalibrationManager",
            "开始多图像外参标定，图像数量: " + std::to_string(image_paths.size()));

        ExtrinsicCalibrationResult result;
        ErrorCode error_code = extrinsic_calibrator_->calibrateMultipleImages(image_paths, result);

        if (error_code == ErrorCode::SUCCESS) {
            // 保存标定结果
            if (!extrinsic_calibrator_->saveCalibrationResult(result, output_path)) {
                LOG_WARN_FUNC("CalibrationManager", "保存标定结果失败");
            }

            LOG_INFO_FUNC("CalibrationManager",
                "多图像外参标定完成，成功处理 " + std::to_string(result.num_successful_images) +
                " 张图像");
        } else {
            LOG_ERROR_FUNC("CalibrationManager", "多图像外参标定失败: " + errorCodeToString(error_code));
        }

        return error_code;

    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("CalibrationManager", "多图像外参标定异常: " + std::string(e.what()));
        return ErrorCode::EXTRINSIC_CALIB_FAILED;
    }
}

} // namespace core
} // namespace camera_calibration
